import React, { useState } from 'react';
import { View, Text } from 'react-native';
import useTranslate from 'mobile/src/hooks/useTranslate';
import ProfilePreferenceList from 'mobile/src/components/ProfilePreferenceList';
import styles from 'mobile/src/components/ProfilePreferenceList/styles';
import utils from 'mobile/src/utils/index';
import { normalizeHeight } from 'mobile/src/styles/Mixins';

const ProfileStreamingQuality = () => {
  const { getText } = useTranslate();
  const [selectedQuality, setSelectedQuality] = useState('Higher');

  const qualityOptions = [
    {
      id: 'higher',
      name: 'Higher',
      description: 'Best video & audio quality, up to 0.8GB/hr',
      selected: true,
    },
    {
      id: 'standard',
      name: 'Standard',
      description: 'Standard video & audio quality, up to 0.6GB/hr',
      selected: false,
    },
    {
      id: 'lower',
      name: 'Lower',
      description: 'Basic video & audio quality, up to 0.4GB/hr',
      selected: false,
    },
  ];

  const onQualitySelect = (quality) => {
    setSelectedQuality(quality.name);
    // Here you would typically save the selection to state/storage
    console.log('Selected streaming quality:', quality.name);
  };

  const renderQualityContent = (item) => (
    <View>
      <Text style={styles.itemName}>{item.name}</Text>
      <Text style={styles.itemDescription}>{item.description}</Text>
    </View>
  );

  const customItemStyle = {
    minHeight: utils.isTab ? normalizeHeight(70) : normalizeHeight(60),
  };

  return (
    <ProfilePreferenceList
      title={getText('streaming_quality') || 'Streaming Quality'}
      data={qualityOptions}
      selectedValue={selectedQuality}
      onItemSelect={onQualitySelect}
      renderItemContent={renderQualityContent}
      itemStyle={customItemStyle}
    />
  );
};

export default ProfileStreamingQuality;
