import React, { useState } from 'react';
import {
  View,
  TouchableOpacity,
  Text,
  FlatList,
  StatusBar,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import useTranslate from 'mobile/src/hooks/useTranslate';
import { BackArrow } from '@video-ready/common/utils/imageConstants';
import AppGradient from 'mobile/src/components/AppBackground';
import { COLORS } from '@video-ready/common/utils';
import styles from './styles';

const ProfileStreamingQuality = () => {
  const { getText } = useTranslate();
  const navigation = useNavigation();

  const [selectedQuality, setSelectedQuality] = useState('Higher');

  const qualityOptions = [
    {
      id: 'higher',
      name: 'Higher',
      description: 'Best video & audio quality, up to 0.8GB/hr',
      selected: true,
    },
    {
      id: 'standard',
      name: 'Standard',
      description: 'Standard video & audio quality, up to 0.6GB/hr',
      selected: false,
    },
    {
      id: 'lower',
      name: 'Lower',
      description: 'Basic video & audio quality, up to 0.4GB/hr',
      selected: false,
    },
  ];

  const onBackPress = () => {
    navigation.goBack();
  };

  const onQualitySelect = (quality) => {
    setSelectedQuality(quality.name);
    // Here you would typically save the selection to state/storage
    console.log('Selected streaming quality:', quality.name);
  };

  const renderQualityItem = ({ item }) => (
    <TouchableOpacity
      style={styles.qualityItem}
      onPress={() => onQualitySelect(item)}
    >
      <View style={styles.qualityContent}>
        <Text style={styles.qualityName}>{item.name}</Text>
        <Text style={styles.qualityDescription}>{item.description}</Text>
      </View>
      {selectedQuality === item.name && (
        <View style={styles.checkmark}>
          <Text style={styles.checkmarkIcon}>✓</Text>
        </View>
      )}
    </TouchableOpacity>
  );

  return (
    <>
      <StatusBar backgroundColor='#0000001A' barStyle='light-content' />
      <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
        <View style={styles.appGradient}>
          <AppGradient />
        </View>
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={onBackPress}>
            <BackArrow width={24} height={24} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>
            {getText('streaming_quality') || 'Streaming Quality'}
          </Text>
        </View>

        <FlatList
          data={qualityOptions}
          renderItem={renderQualityItem}
          keyExtractor={(item) => item.id}
          showsVerticalScrollIndicator={false}
        />
      </SafeAreaView>
    </>
  );
};

export default ProfileStreamingQuality;
