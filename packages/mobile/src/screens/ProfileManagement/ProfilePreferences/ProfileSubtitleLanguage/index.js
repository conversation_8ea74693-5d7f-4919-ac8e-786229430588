import React, { useState } from 'react';
import {
  View,
  TouchableOpacity,
  Text,
  FlatList,
  StatusBar,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import useTranslate from 'mobile/src/hooks/useTranslate';
import { BackArrow } from '@video-ready/common/utils/imageConstants';
import AppGradient from 'mobile/src/components/AppBackground';
import { COLORS } from '@video-ready/common/utils';
import styles from './styles';
const subtitleOptions = [
  { id: 'en', name: 'English', selected: true },
  { id: 'ms', name: 'Bahasa Malaysia', selected: false },
  { id: 'zh', name: 'Chinese', selected: false },
  { id: 'ta', name: 'Tamil', selected: false },
  { id: 'off', name: 'Off', selected: false },
];

const ProfileSubtitleLanguage = () => {
  const { getText } = useTranslate();
  const navigation = useNavigation();

  const [selectedSubtitle, setSelectedSubtitle] = useState('English');

  const onBackPress = () => {
    navigation.goBack();
  };

  const onSubtitleSelect = (subtitle) => {
    setSelectedSubtitle(subtitle.name);
    // Here you would typically save the selection to state/storage
    console.log('Selected subtitle language:', subtitle.name);
  };

  const renderSubtitleItem = ({ item }) => (
    <TouchableOpacity
      style={styles.subtitleItem}
      onPress={() => onSubtitleSelect(item)}
    >
      <Text style={styles.subtitleName}>{item.name}</Text>
      {selectedSubtitle === item.name && (
        <View style={styles.checkmark}>
          <Text style={styles.checkmarkIcon}>✓</Text>
        </View>
      )}
    </TouchableOpacity>
  );

  return (
    <>
      <StatusBar backgroundColor='#0000001A' barStyle='light-content' />
      <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
        <View style={styles.appGradient}>
          <AppGradient />
        </View>
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={onBackPress}>
            <BackArrow width={24} height={24} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>
            {getText('subtitle_language') || 'Subtitle Language'}
          </Text>
        </View>

        <FlatList
          data={subtitleOptions}
          renderItem={renderSubtitleItem}
          keyExtractor={(item) => item.id}
          showsVerticalScrollIndicator={false}
        />
      </SafeAreaView>
    </>
  );
};

export default ProfileSubtitleLanguage;
