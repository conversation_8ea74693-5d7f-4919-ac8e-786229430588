import React, { useState } from 'react';
import useTranslate from 'mobile/src/hooks/useTranslate';
import ProfilePreferenceList from 'mobile/src/components/ProfilePreferenceList';
const subtitleOptions = [
  { id: 'en', name: 'English', selected: true },
  { id: 'ms', name: 'Bahasa Malaysia', selected: false },
  { id: 'zh', name: 'Chinese', selected: false },
  { id: 'ta', name: 'Tamil', selected: false },
  { id: 'off', name: 'Off', selected: false },
];

const ProfileSubtitleLanguage = () => {
  const { getText } = useTranslate();
  const [selectedSubtitle, setSelectedSubtitle] = useState('English');

  const onSubtitleSelect = (subtitle) => {
    setSelectedSubtitle(subtitle.name);
    // Here you would typically save the selection to state/storage
    console.log('Selected subtitle language:', subtitle.name);
  };

  return (
    <ProfilePreferenceList
      title={getText('subtitle_language') || 'Subtitle Language'}
      data={subtitleOptions}
      selectedValue={selectedSubtitle}
      onItemSelect={onSubtitleSelect}
    />
  );
};

export default ProfileSubtitleLanguage;
