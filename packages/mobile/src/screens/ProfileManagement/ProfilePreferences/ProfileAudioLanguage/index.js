import React, { useState } from 'react';
import useTranslate from 'mobile/src/hooks/useTranslate';
import ProfilePreferenceList from 'mobile/src/components/ProfilePreferenceList';

const ProfileAudioLanguage = () => {
  const { getText } = useTranslate();
  const [selectedLanguage, setSelectedLanguage] = useState('English');

  const audioLanguages = [
    { id: 'en', name: 'English', selected: true },
    { id: 'ms', name: 'Bahasa Malaysia', selected: false },
    { id: 'zh', name: 'Chinese', selected: false },
    { id: 'ta', name: 'Tamil', selected: false },
  ];

  const onLanguageSelect = (language) => {
    setSelectedLanguage(language.name);
    // Here you would typically save the selection to state/storage
    console.log('Selected audio language:', language.name);
  };

  return (
    <ProfilePreferenceList
      title={getText('audio_language') || 'Audio Language'}
      data={audioLanguages}
      selectedValue={selectedLanguage}
      onItemSelect={onLanguageSelect}
    />
  );
};

export default ProfileAudioLanguage;
