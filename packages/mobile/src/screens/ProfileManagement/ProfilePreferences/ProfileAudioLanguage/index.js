import React, { useState } from 'react';
import {
  View,
  TouchableOpacity,
  Text,
  FlatList,
  StatusBar,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import useTranslate from 'mobile/src/hooks/useTranslate';
import { BackArrow } from '@video-ready/common/utils/imageConstants';
import AppGradient from 'mobile/src/components/AppBackground';
import { COLORS } from '@video-ready/common/utils';
import styles from './styles';

const ProfileAudioLanguage = () => {
  const { getText } = useTranslate();
  const navigation = useNavigation();

  const [selectedLanguage, setSelectedLanguage] = useState('English');

  const audioLanguages = [
    { id: 'en', name: 'English', selected: true },
    { id: 'ms', name: 'Bahasa Malaysia', selected: false },
    { id: 'zh', name: 'Chinese', selected: false },
    { id: 'ta', name: 'Tamil', selected: false },
  ];

  const onBackPress = () => {
    navigation.goBack();
  };

  const onLanguageSelect = (language) => {
    setSelectedLanguage(language.name);
    // Here you would typically save the selection to state/storage
    console.log('Selected audio language:', language.name);
  };

  const renderLanguageItem = ({ item }) => (
    <TouchableOpacity
      style={styles.languageItem}
      onPress={() => onLanguageSelect(item)}
    >
      <Text style={styles.languageName}>{item.name}</Text>
      {selectedLanguage === item.name && (
        <View style={styles.checkmark}>
          <Text style={styles.checkmarkIcon}>✓</Text>
        </View>
      )}
    </TouchableOpacity>
  );

  return (
    <>
      <StatusBar backgroundColor='#0000001A' barStyle='light-content' />
      <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
        <View style={styles.appGradient}>
          <AppGradient />
        </View>
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={onBackPress}>
            <BackArrow width={24} height={24} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>
            {getText('audio_language') || 'Audio Language'}
          </Text>
        </View>

        <FlatList
          data={audioLanguages}
          renderItem={renderLanguageItem}
          keyExtractor={(item) => item.id}
          showsVerticalScrollIndicator={false}
        />
      </SafeAreaView>
    </>
  );
};

export default ProfileAudioLanguage;
