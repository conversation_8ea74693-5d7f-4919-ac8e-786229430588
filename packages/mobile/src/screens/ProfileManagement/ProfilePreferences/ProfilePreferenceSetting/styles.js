import { StyleSheet } from 'react-native';
import { COLORS, FONTS } from '@video-ready/common/utils';
import {
  normalizeFont,
  normalizeHeight,
  normalizeWidth,
} from 'mobile/src/styles/Mixins';
import utils from 'mobile/src/utils/index';

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.COLOR_1F0C1ECC,
  },
  content: {
    flex: 1,
    paddingHorizontal: normalizeWidth(16),
    paddingTop: normalizeHeight(24),
  },
  headerTitle: {
    fontSize: utils.isTab ? normalizeFont(32) : normalizeFont(24),
    fontFamily: FONTS.MULISH_BOLD,
    color: COLORS.COLOR_EBEBEB,
    textAlign: 'center',
    marginBottom: normalizeHeight(32),
  },
  preferencesContainer: {
    backgroundColor: COLORS.COLOR_0000004D,
    borderRadius: normalizeHeight(12),
    overflow: 'hidden',
  },
  preferenceItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: utils.isTab ? normalizeHeight(24) : normalizeHeight(16),
    paddingHorizontal: utils.isTab ? normalizeWidth(32) : normalizeWidth(20),
    minHeight: utils.isTab ? normalizeHeight(80) : normalizeHeight(60),
  },
  preferenceContent: {
    flex: 1,
  },
  preferenceTitle: {
    fontSize: utils.isTab ? normalizeFont(20) : normalizeFont(16),
    fontFamily: FONTS.MULISH_BOLD,
    color: COLORS.COLOR_EBEBEB,
    marginBottom: normalizeHeight(4),
  },
  preferenceValue: {
    fontSize: utils.isTab ? normalizeFont(16) : normalizeFont(14),
    fontFamily: FONTS.MULISH_MEDIUM,
    color: COLORS.COLOR_EBEBEBB2,
  },
  chevronContainer: {
    marginLeft: normalizeWidth(16),
  },
  chevronIcon: {
    fontSize: utils.isTab ? normalizeFont(28) : normalizeFont(24),
    color: COLORS.COLOR_EBEBEB,
    fontWeight: 'bold',
  },
  separator: {
    height: normalizeHeight(1),
    backgroundColor: COLORS.COLOR_FFFFFF33,
    marginHorizontal: utils.isTab ? normalizeWidth(32) : normalizeWidth(20),
  },
});
