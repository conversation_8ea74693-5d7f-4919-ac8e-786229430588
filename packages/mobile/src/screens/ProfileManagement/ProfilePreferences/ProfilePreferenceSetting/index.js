import React, { useState } from 'react';
import { View, TouchableOpacity, Text, SafeAreaView } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import useTranslate from 'mobile/src/hooks/useTranslate';
import Routenames from 'mobile/src/navigation/routeName';
import styles from './styles';

const ProfilePreferenceSetting = () => {
  const { getText } = useTranslate();
  const navigation = useNavigation();

  // State for preferences
  const [streamingQuality, setStreamingQuality] = useState('Higher');
  const [audioLanguage, setAudioLanguage] = useState('English');
  const [subtitles, setSubtitles] = useState('Off');

  const preferenceOptions = [
    {
      id: 'streaming_quality',
      title: getText('streaming_quality') || 'Streaming Quality',
      value: streamingQuality,
      onPress: () => {
        console.log('Streaming Quality');
        navigation.navigate(Routenames.PROFILE_STREAMING_QUALITY);
      },
    },
    {
      id: 'audio_language',
      title: getText('audio_language') || 'Audio Language',
      value: audioLanguage,
      onPress: () => {
        navigation.navigate(Routenames.PROFILE_AUDIO_LANGUAGE);
      },
    },
    {
      id: 'subtitles',
      title: getText('subtitles') || 'Subtitles',
      value: subtitles,
      onPress: () => {
        navigation.navigate(Routenames.PROFILE_SUBTITLE_LANGUAGE);
      },
    },
  ];

  const renderPreferenceItem = ({ item }) => (
    <TouchableOpacity style={styles.preferenceItem} onPress={item.onPress}>
      <View style={styles.preferenceContent}>
        <Text style={styles.preferenceTitle}>{item.title}</Text>
        <Text style={styles.preferenceValue}>{item.value}</Text>
      </View>
      <View style={styles.chevronContainer}>
        <Text style={styles.chevronIcon}>›</Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.headerTitle}>
          {getText('profile_preferences') || 'Profile Preferences'}
        </Text>

        <View style={styles.preferencesContainer}>
          {preferenceOptions.map((item, index) => (
            <View key={item.id}>
              {renderPreferenceItem({ item })}
              {index < preferenceOptions.length - 1 && (
                <View style={styles.separator} />
              )}
            </View>
          ))}
        </View>
      </View>
    </SafeAreaView>
  );
};

export default ProfilePreferenceSetting;
