import { useState, useCallback } from 'react';
import useTranslate from 'mobile/src/hooks/useTranslate';
import Routenames from '../../../navigation/routeName';

const useCreateProfile = ({ navigation, avatar }) => {
  const { getText } = useTranslate();

  // State management for form fields and modal
  const [showModal, setShowModal] = useState(false);
  const [name, setName] = useState('');
  const [ageGroup, setAgeGroup] = useState(null);
  const [ageGroups, setAgeGroups] = useState([
    {
      label: 'Adults',
      value: 'adults',
      description:
        'No age restrictions in channel listings and On-Demand catalogue.',
    },
    {
      label: 'Kids',
      value: 'kids',
      description:
        'Only kids friendly channels and On-Demand catalogue will be displayed.',
    },
  ]);
  const [language, setLanguage] = useState(null);
  const [languages, setLanguages] = useState([
    { label: 'English', value: 'en' },
    { label: 'Hindi', value: 'hi' },
    { label: 'Tamil', value: 'ta' },
  ]);
  const [currentField, setCurrentField] = useState(null);
  const [modalData, setModalData] = useState([]);

  // Handles selection from modal and updates corresponding state
  const handleModalSelection = useCallback(
    (selectedItem) => {
      if (currentField === 'ageGroup') {
        setAgeGroup(selectedItem);
      } else if (currentField === 'language') {
        setLanguage(selectedItem);
      }
    },
    [currentField]
  );

  // Handles opening the modal with appropriate data
  const handleOpenModal = useCallback((field, data) => {
    setModalData(data);
    setCurrentField(field);
    setShowModal(true);
  }, []);

  // Handles navigation to profile preference settings
  const handleCreateProfile = useCallback(() => {
    navigation.navigate(Routenames.PROFILE_PREFERENCE_SETTING, {
      navigation,
      name,
      ageGroup,
      language,
      avatar,
    });
  }, [navigation, name, ageGroup, language, avatar]);

  // Handles navigation back
  const handleGoBack = useCallback(() => {
    navigation.navigate(Routenames.PROFILE_MANAGEMENT);
  }, [navigation]);

  return {
    showModal,
    setShowModal,
    name,
    setName,
    ageGroup,
    language,
    ageGroups,
    languages,
    currentField,
    modalData,
    handleModalSelection,
    handleOpenModal,
    handleCreateProfile,
    handleGoBack,
    getText,
  };
};

export default useCreateProfile;
