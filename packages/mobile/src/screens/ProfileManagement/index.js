import React, { useCallback, useMemo } from 'react';
import {
  View,
  Text,
  FlatList,
  SafeAreaView,
  TouchableOpacity,
} from 'react-native';
import utils from 'mobile/src/utils';
import styles from 'mobile/src/screens/ProfileManagement/styles';
import { useNavigation } from '@react-navigation/native';
import useTranslate from 'mobile/src/hooks/useTranslate';
import { AstroLogoSplash } from '@video-ready/common/utils/imageConstants';
import { profiles } from 'mobile/src/screens/ProfileManagement/helper';
import ProfileListing from 'mobile/src/screens/ProfileManagement/Components/ProfileList';
import Routenames from 'mobile/src/navigation/routeName';

const ProfileManagement = () => {
  const { getText } = useTranslate();
  const navigation = useNavigation();

  // Renders each profile item in the list, passing necessary props to ProfileListing component
  const renderItem = useCallback(
    ({ item, index }) => (
      <ProfileListing
        item={item}
        index={index}
        navigation={navigation}
        getText={getText}
      />
    ),
    [navigation, getText]
  );

  // Create a list of profiles that includes a "plus" button if there are fewer than 5 profiles
  const listProfileData = useMemo(
    () =>
      profiles?.length < 5
        ? [...profiles, { id: 'plus', isPlusView: true }]
        : profiles,
    [profiles]
  );

  return (
    <SafeAreaView style={styles.mainContainer}>
      <View style={styles.container}>
        <View style={styles.whosWatchingView}>
          <Text style={styles.whosWatchingText}>{getText('who_watching')}</Text>
          <View style={styles.astroLogo}>
            <AstroLogoSplash style={[styles.logoIcon]} />
          </View>
        </View>
        <FlatList
          data={listProfileData}
          renderItem={renderItem}
          contentContainerStyle={[styles.containerStyle]}
          horizontal={utils?.isTab}
        />
        <TouchableOpacity
          style={styles.profilePreferencesButton}
          onPress={() =>
            navigation.navigate(Routenames.PROFILE_PREFERENCE_SETTING)
          }
        >
          <Text style={styles.profilePreferencesButtonText}>
            {getText('profile_preferences') || 'Profile Preferences'}
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

export default ProfileManagement;
