import React, { useState, useCallback } from 'react';
import {
  Text,
  TouchableOpacity,
  Modal,
  FlatList,
  SafeAreaView,
  Pressable,
} from 'react-native';
import styles from 'mobile/src/screens/ProfileManagement/styles';
import LinearGradient from 'react-native-linear-gradient';
import { COLORS } from '@video-ready/common/utils';
import { CrossPlayer } from '@video-ready/common/utils/imageConstants';
import { normalizeHeight } from 'mobile/src/styles/Mixins';

const ProfileModal = ({
  data,
  showModal,
  setShowModal = () => {},
  onPress = () => {},
}) => {
  const [selectedItem, setSelectedItem] = useState(null);

  // Renders each item in the modal list with selection handling
  const renderItem = useCallback(
    ({ item, index }) => {
      return (
        <Pressable
          hitSlop={5}
          onPress={() => {
            setSelectedItem(item);
            onPress(item);
            setShowModal(false);
          }}
          style={[
            styles.ratingView,
            selectedItem?.label === item?.label && styles.ratingActive,
          ]}
          testID={`modal-item-${index}`}
        >
          <Text style={[styles.ratingText]}>{item?.label}</Text>
        </Pressable>
      );
    },
    [selectedItem, onPress, setShowModal]
  );

  return (
    <Modal
      visible={showModal}
      animationType={'fade'}
      transparent={true}
      onRequestClose={() => setShowModal(false)}
    >
      <SafeAreaView style={styles.safeArea}>
        <LinearGradient
          colors={[COLORS.COLOR_1F0C1E, COLORS.COLOR_1F0C1E]}
          style={styles.modalGradient}
        />
        <TouchableOpacity
          onPress={() => {
            setShowModal(false);
          }}
          style={styles.crossView}
          testID='modal-close-button'
        >
          <CrossPlayer
            height={normalizeHeight(24)}
            width={normalizeHeight(24)}
          />
        </TouchableOpacity>
        <FlatList
          data={data}
          renderItem={renderItem}
          contentContainerStyle={styles.containerStyleModal}
        />
      </SafeAreaView>
    </Modal>
  );
};

export default ProfileModal;
