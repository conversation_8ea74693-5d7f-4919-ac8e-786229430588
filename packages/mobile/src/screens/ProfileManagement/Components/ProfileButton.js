import React from 'react';
import { Text, TouchableOpacity } from 'react-native';
import styles from 'mobile/src/screens/ProfileManagement/styles';

const ProfileButton = ({
  title = ' ',
  disable = false,
  onPress = () => {},
}) => {
  return (
    <TouchableOpacity
      style={styles.profileButtonStyle}
      onPress={onPress}
      disabled={disable}
    >
      <Text style={styles.buttonText(disable)}>{title}</Text>
    </TouchableOpacity>
  );
};

export default ProfileButton;
