import { StyleSheet } from 'react-native';
import { COLORS, FONTS } from '@video-ready/common/utils';
import {
  normalize,
  normalizeFont,
  normalizeHeight,
  normalizeWidth,
} from 'mobile/src/styles/Mixins';
import utils, { ScreenHeight, ScreenWidth } from 'mobile/src/utils/index';

export default StyleSheet.create({
  avatarStyle: {
    height: utils.isTab ? normalizeHeight(143) : normalizeHeight(64),
    width: utils.isTab ? normalizeHeight(143) : normalizeHeight(64),
    borderRadius: utils?.isTab ? normalizeHeight(72) : normalizeHeight(32),
    backgroundColor: COLORS.COLOR_EEEEEE,
  },
  controlsLayout: {
    height: utils?.isTab ? normalizeHeight(28) : normalizeHeight(24),
    width: utils?.isTab ? normalizeHeight(28) : normalizeHeight(24),
    overflow: 'hidden',
  },
  whosWatchingText: {
    fontSize: utils.isTab ? normalizeFont(35) : normalizeFont(24),
    lineHeight: normalizeHeight(32),
    fontFamily: FONTS.MULISH_BOLD,
    textAlign: 'center',
    color: COLORS.COLOR_EBEBEB,
    ...(utils.isTab && { marginRight: normalizeWidth(12) }),
  },
  logoIcon: {
    height: '100%',
    top: '0%',
    right: '0%',
    bottom: '0%',
    left: '0%',
    maxWidth: '100%',
    maxHeight: '100%',
    overflow: 'hidden',
  },
  astroLogo: {
    width: utils?.isTab ? normalizeWidth(129) : normalizeWidth(93),
    height: utils?.isTab ? normalizeWidth(56) : normalizeWidth(40),
    ...(!utils.isTab && { marginTop: normalizeHeight(6) }),
  },
  whosWatchingView: {
    alignItems: 'center',
    ...(utils.isTab && { flexDirection: 'row' }),
    marginBottom: utils.isTab ? normalizeHeight(43) : normalizeHeight(24),
  },
  avatarView: {
    overflow: 'hidden',
    marginHorizontal: normalizeWidth(24),
    ...(utils.isTab && { marginBottom: normalizeHeight(16) }),
  },
  name: {
    fontSize: normalizeFont(20),
    lineHeight: normalizeHeight(30),
    fontFamily: FONTS.MULISH_MEDIUM,
    color: COLORS.COLOR_EBEBEB,
    textShadowColor: COLORS.COLOR_0000004D,
    textShadowOffset: {
      width: 0,
      height: normalizeHeight(2),
    },
    textShadowRadius: normalizeHeight(4),
    ...(utils.isTab && { marginBottom: normalizeHeight(16) }),
  },
  plusView: {
    backgroundColor: COLORS.COLOR_0000004D,
    height: utils.isTab ? normalizeHeight(143) : normalizeHeight(64),
    width: utils.isTab ? normalizeHeight(143) : normalizeHeight(64),
    borderRadius: utils?.isTab ? normalizeHeight(72) : normalizeHeight(32),
    alignItems: 'center',
    justifyContent: 'center',
    ...(utils.isTab && { marginBottom: normalizeHeight(16) }),
    ...(!utils.isTab && { left: normalizeWidth(48) }),
    borderColor: COLORS.COLOR_FFFFFF33,
    borderWidth: normalizeWidth(1),
  },
  itemContainer: {
    flex: 1,
    ...(!utils.isTab && { flexDirection: 'row' }),
    alignItems: 'center',
    ...(utils.isTab && { marginRight: normalizeHeight(32) }),
    marginBottom: normalizeWidth(24),
  },
  mainContainer: {
    backgroundColor: COLORS.COLOR_1F0C1ECC,
    flex: 1,
    justifyContent: 'center',
  },
  container: {
    maxHeight: normalizeHeight(526),
    alignItems: 'center',
  },
  containerStyle: {
    ...(utils.isTab && {
      flexDirection: 'row',
      justifyContent: 'center',
    }),
  },
  chevronLeftIcon: {
    overflow: 'hidden',
    marginRight: normalizeWidth(8),
  },
  title: {
    fontSize: normalizeFont(16),
    fontFamily: FONTS.MULISH_BOLD,
    color: COLORS.COLOR_EBEBEB,
    textAlign: 'left',
    textShadowColor: COLORS.COLOR_0000004D,
    textShadowOffset: {
      width: 0,
      height: normalizeHeight(2),
    },
    textShadowRadius: normalizeWidth(4),
    maxHeight: normalizeHeight(20),
    overflow: 'hidden',
    flex: 1,
  },
  leftParentHeader: {
    flex: 1,
    alignItems: 'center',
    flexDirection: 'row',
  },
  headerContainer: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: normalizeWidth(12),
    paddingVertical: normalizeHeight(4),
    marginHorizontal: normalizeWidth(12),
  },
  profileButtonStyle: {
    flex: 1,
    height: normalizeHeight(48),
    backgroundColor: COLORS.COLOR_0000004D,
    alignItems: 'center',
    justifyContent: 'center',
    borderColor: COLORS.COLOR_FFFFFF33,
    borderWidth: normalizeWidth(1),
    borderRadius: normalizeHeight(8),
  },
  buttonText: (disable) => ({
    fontSize: normalizeFont(16),
    color: COLORS.COLOR_EBEBEB,
    fontFamily: FONTS.MULISH_BOLD,
    ...(disable && {
      opacity: 0.5,
      fontSize: normalizeFont(14),
    }),
  }),
  modalGradient: {
    ...StyleSheet.absoluteFillObject,
    opacity: 0.75,
    alignItems: 'center',
    justifyContent: 'center',
  },
  ratingView: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    borderRadius: normalizeWidth(24),
    marginBottom: normalizeHeight(16),
    paddingVertical: normalizeHeight(12),
    paddingHorizontal: normalizeWidth(16),
  },
  ratingActive: {
    borderColor: COLORS.COLOR_FFFFFF80,
    borderRadius: normalize(8),
    borderWidth: normalizeWidth(2),
    alignSelf: 'center',
  },
  ratingText: {
    fontSize: normalizeFont(16),
    color: COLORS.COLOR_EBEBEB,
    lineHeight: normalizeHeight(20),
    fontFamily: FONTS.MULISH_BOLD,
    borderWidth: normalize(2),
    borderColor: COLORS.TRANSPARENT,
  },
  safeArea: {
    height: '100%',
    width: '100%',
    backgroundColor: COLORS.COLOR_1F0C1EBF,
  },
  crossView: {
    alignSelf: 'flex-end',
    marginRight: normalizeWidth(20),
  },
  containerStyleModal: {
    height: '100%',
    justifyContent: 'center',
  },
  profilePreferencesButton: {
    backgroundColor: COLORS.COLOR_0000004D,
    borderColor: COLORS.COLOR_FFFFFF33,
    borderWidth: normalizeWidth(1),
    borderRadius: normalizeHeight(8),
    paddingVertical: utils.isTab ? normalizeHeight(16) : normalizeHeight(12),
    paddingHorizontal: utils.isTab ? normalizeWidth(32) : normalizeWidth(24),
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: normalizeHeight(24),
    marginHorizontal: normalizeWidth(16),
  },
  profilePreferencesButtonText: {
    fontSize: utils.isTab ? normalizeFont(18) : normalizeFont(16),
    fontFamily: FONTS.MULISH_BOLD,
    color: COLORS.COLOR_EBEBEB,
    textAlign: 'center',
  },
});
