import { useCallback, useEffect, useRef, useState } from 'react';

import { useObservable } from '@legendapp/state/react';
import { useSelector, useDispatch } from 'react-redux';

import {
  decryptUrls,
  prepareSkipInfo,
  defaultSkipInfo,
  flatContentPlayUrls,
  logPlayerErrorToSentry,
  getConvivaContentInfoVod,
  checkVideoStreamUrlIsValid,
  parseMovieWatchHistoryData,
  getUserEntitlementBasedMaxResolution,
} from '../helper';
import utils from 'mobile/src/utils';
import {
  addImagePrefixUrl,
  getOpenSeriesWatchedTitle,
  getClosedSeriesWatchedTitle,
} from 'mobile/src/utils/commonMethods';
import {
  fetchEpisodeData,
  fetchContentData,
  fetchFirstEpisode,
  fetchSeriesMetaData,
  updateContinueWatchData,
  removeContentFromContinueWatching,
  getMovieContinueWatchByContentIdMobile,
} from '@video-ready/common/services/home/<USER>';
import appConfig from '@video-ready/common/utils/config';
import { V3_CONTENT_TYPE } from 'mobile/src/appConstants';
import {
  getCdnWatermarkToken,
  getResolutionCapping,
  getDrmTokenByContentIdMobile,
  getCdnWatermarkTokenUnauthorized,
} from '@video-ready/common/services/miscellaneous/action';
import CONSTANTS from '@video-ready/common/utils/constant';
import { PLAYER_GENERIC_ERROR_CODE } from '../playerConstants';
import { flattenGenres } from 'mobile/src/screens/ContentPage/helper';
import { CONVIVA_ACCOUNT_TYPE } from 'mobile/src/utils/convivaMethods';
import supportedDeviceInfo from 'mobile/src/configs/supportedDeviceInfo';
import { checkForContentEntitlement } from 'mobile/src/utils/commonMethods';
import { getPurchaseDetails } from '@video-ready/common/utils/commonMethods';
import DownloadManager from 'mobile/src/downloadplayer/utils/DownloadManager';

const defaultMetaData = {
  genre: [],
  title: null,
  assetId: null,
  ageRating: null,
  contentId: null,
  posterUrl: null,
  playbackUrl: null,
  isVodPlayer: true,
  contentType: null,
  entitlements: null,
  drmConfiguration: null,
  adsConfiguration: null,
  externalSubtitleUrl: null,
  externalThumbnailUrl: null,
  selectedSeasonNumber: null,
  selectedEpisodeNumber: null,
  secondsOfContentWatched: null,
};

/**
 * Custom hook for preparing playback metadata.
 * @param {*} props - Data of the content for which playback metadata needs to be prepared.
 * @returns {Object} - Object containing the prepared metadata for playback.
 */

function usePrepareOfflinePlaybackVod(props) {
  const { fromViewAll = false } = props;
  const dispatch = useDispatch();
  const control$ = useObservable(props.control$);
  const { device_id } = supportedDeviceInfo.get();

  const resolutionConfig = useSelector(
    (state) => state?.auth?.resolutionConfig
  );
  const assetDetailsData = useSelector(
    (state) => state?.home?.assetDetailsData
  );
  const firstEpisodeData = useSelector(
    (state) => state?.home?.firstEpisodeDetails
  );

  const userEntitlementInfoData = useSelector(
    (state) => state?.auth?.userEntitlementInfo
  );
  const activeSubscriptions = useSelector(
    (state) => state?.auth?.userEntitlement
  );
  const userData = useSelector((state) => state?.auth?.signInResponse?.data);
  const cwConfigMetaData = useSelector((state) => state?.auth?.cwConfigData);

  const nextContentData = useRef(null);
  const contentData = useRef(props?.contentData?.meta);
  const callRemoveLastContentFromWatchHistory = useRef(true);

  const [metaData, setMetaData] = useState(defaultMetaData);
  const [convivaContentInfo, setConvivaContentInfo] = useState({});
  const [secondsOfContentWatched, setSecondsOfContentWatched] = useState(0);
  const [playTrailer, setPlayTrailer] = useState(props?.playTrailer ?? false);

  const accessToken = userData?.tokenDetails?.accessToken;

  const cwConfigInfo =
    V3_CONTENT_TYPE.MOVIE === contentData.current?.ContentType
      ? cwConfigMetaData?.movie
      : cwConfigMetaData?.web_series;

  const cwEndThreshold = cwConfigInfo?.exit_threshold;
  const cwStartThreshold = cwConfigInfo?.start_threshold;
  const cwPollingInterval = cwConfigInfo?.polling_time_interval;
  const action = 'stream';

  const isWaterMarkTokenRequired = appConfig?.getWaterMarkTokenRequired();

  const errorCallBack = useCallback(
    (errorCode = PLAYER_GENERIC_ERROR_CODE.GENERIC_FAILURE, errorBody = '') => {
      console.log('errorCallBack', errorCode, errorBody);
      control$?.error?.set?.(true);
      // Report Error on Sentry
      logPlayerErrorToSentry({
        message: errorCode,
        details: { errorStack: JSON.stringify(errorBody) },
      });
      setMetaData(defaultMetaData);
    },
    [control$?.error]
  );

  /**  Constructs the request body for fetching tokens, such as Watermark Token (WMT) and DRM Token.
   * It returns an object with various properties like action, content type, content ID, and entitlement information.
   *`isWMT`: A flag to include additional Watermark-related properties when true.
   * The `intersection` array is calculated as the common elements between the channel's entitlements
   * and the user's total plans, and is needed specifically for SVOD (Subscription Video on Demand).
   * The function includes device type and subscriber information.
   * If `isWMT` is true, additional properties related to Watermark Token are included, such as `type` (indicating iOS),
   * CDN list, and authentication flags for Watermark and CDN.
   */

  const getTokenBody = useCallback(
    (
      { entitlements = [], ...rest },
      isWMT = false,
      extractedTrailerLaContentId = ''
    ) => {
      let intersection = [];
      if (Array.isArray(entitlements) && userEntitlementInfoData?.activePlans) {
        intersection = entitlements.filter(
          (skuCode) => userEntitlementInfoData?.activePlans?.[skuCode]
        );
        intersection = [...new Set(intersection)];
      }
      return {
        action: action,
        contentType: 'vod',
        intersection: intersection, // need intersection for SVOD
        deviceType: utils.platform,
        contentId: rest?.contentId,
        entitlementList: entitlements,
        subscriberId: userData?.subscriber?.customerId,
        ...(isWMT && {
          type: Number(utils?.isIOS),
          cdnList: rest?.cdnList ?? [],
          isWMAuthEnabled: rest?.isWMAuthEnabled,
          isCDNAuthEnabled: rest?.isCDNAuthEnabled,
        }),
        ...(!isWMT && {
          laContentId: rest?.laContentId,
        }),
        ...((playTrailer || props?.contentType === V3_CONTENT_TYPE.TRAILER) &&
          isWMT &&
          extractedTrailerLaContentId && {
            laContentId: extractedTrailerLaContentId,
          }),
      };
    },
    [
      playTrailer,
      props?.contentType,
      userData?.subscriber?.customerId,
      userEntitlementInfoData?.activePlans,
    ]
  );

  /**
  This function fetches the Watermark token if CDN authentication is enabled for the current channel.
  It returns a promise that either resolves with the token or null (if CDN authentication is not enabled).
  If CDN auth is enabled, the function constructs the token request body using `getTokenBody(true)`
  and calls `getCdnWatermarkToken` with the access token and request params.
  The promise resolves with the token on success, or rejects if there is an error.
  */
  const fetchWaterMarkToken = useCallback(
    ({ params = null, contentMetaData = contentData?.current ?? {} }) => {
      return new Promise((resolve, reject) => {
        const isTrailer =
          playTrailer || props?.contentType === V3_CONTENT_TYPE.TRAILER;

        const type = playTrailer
          ? CONSTANTS.UWM_TYPE.EMBEDDED
          : CONSTANTS.UWM_TYPE.FULL;

        if (
          (contentMetaData?.isCDNAuthEnabled ||
            contentMetaData?.isWMAuthEnabled) &&
          isWaterMarkTokenRequired
        ) {
          let extractedTrailerLaContentId;
          if (isTrailer) {
            const licenseAcquisitionUrl = contentMetaData?.playUrlDetail?.find(
              (el) => el?.contentType === CONSTANTS.MediaURLAssetType.preview
            )?.licenseAcquisitionUrl?.widevine;
            const decryptLicenseAcquisitionUrl = decryptUrls(
              licenseAcquisitionUrl
            );
            if (decryptLicenseAcquisitionUrl) {
              const match =
                decryptLicenseAcquisitionUrl.match(/contentId=([^&]+)/);
              extractedTrailerLaContentId = match ? match[1] : null;
            }
          }
          if (accessToken) {
            if (!params) {
              params = getTokenBody(
                contentMetaData ?? {},
                true,
                extractedTrailerLaContentId
              );
            }
            getCdnWatermarkToken(accessToken, params, type)
              .then(resolve)
              .catch((error) =>
                reject(PLAYER_GENERIC_ERROR_CODE.WM_TOKEN_FAIL, error)
              );
          } else if (
            contentMetaData?.subscriptionType.includes(
              CONSTANTS.SUBSCRIPTION_CONTENT.FREE_ANONYMOUS
            ) ||
            isTrailer
          ) {
            getCdnWatermarkTokenUnauthorized({
              laContentId:
                playTrailer || props?.contentType === V3_CONTENT_TYPE.TRAILER
                  ? extractedTrailerLaContentId
                  : contentMetaData?.laContentId,
              ...((playTrailer ||
                props?.contentType === V3_CONTENT_TYPE.TRAILER) && {
                isWMAuthEnabled: contentMetaData?.isWMAuthEnabled,
                isCDNAuthEnabled: contentMetaData?.isCDNAuthEnabled,
              },
              type),
            })
              .then(resolve)
              .catch((error) =>
                reject(
                  PLAYER_GENERIC_ERROR_CODE.WM_TOKEN_FAIL_UNAUTHORIZED,
                  error
                )
              );
          }
        } else {
          resolve(null);
        }
      });
    },
    [accessToken, getTokenBody, playTrailer, props?.contentType]
  );

  /**
   This function fetches the DRM token by creating a new promise.
   It calls `getTokenBody` to retrieve the necessary parameters.
   and then invokes `getDrmTokenByContentIdMobile` with the access token and parameters.
   The promise resolves with the DRM token on success or rejects with an error if the request fails.
   */
  const fetchDrmToken = useCallback(
    ({ params = null, contentMetaData = contentData?.current ?? {} }) => {
      return new Promise((resolve, reject) => {
        // const licenseAcquisitionUrl = contentMetaData?.playUrlDetail?.find(
        //   (el) => el?.contentType === CONSTANTS.MediaURLAssetType.fullAsset
        // )?.licenseAcquisitionUrl?.widevine;
        // const decryptLicenseAcquisitionUrl = decryptUrls(licenseAcquisitionUrl);
        const purchaseDetails = getPurchaseDetails(
          contentMetaData?.purchaseDetails,
          userEntitlementInfoData
        );
        let disableLicenseToken = purchaseDetails?.disableLicenseToken;
        if (
          purchaseDetails &&
          !disableLicenseToken &&
          !playTrailer &&
          contentMetaData?.laContentId
        ) {
          if (!params) {
            params = getTokenBody(contentMetaData ?? {});
          }
          getDrmTokenByContentIdMobile(accessToken, params)
            .then((response) =>
              resolve({
                ...response,
                disableLicenseToken,
              })
            )
            .catch((error) =>
              reject(PLAYER_GENERIC_ERROR_CODE.DRM_TOKEN_FAIL, error)
            );
        } else {
          resolve({
            disableLicenseToken: true,
          });
        }
      });
    },
    [accessToken, getTokenBody, playTrailer, userEntitlementInfoData]
  );

  /**
  * This function fetches both DRM and Watermark tokens simultaneously using `Promise.all`.
  - It takes `params` as an argument and calls `fetchDrmToken` and `fetchWaterMarkToken` in parallel.
  - Once both promises resolve, it extracts the token and expiration time (`expiresIn`) from each response
    and returns an object containing `drmToken`, `wmToken`, `drmTokenExpTime`, and `wmTokenExpTime`.
  - If either request fails, the `errorCallBack` is triggered to handle the error.
  * */
  const fetchTokens = useCallback(
    async (params) => {
      const { contentMetaData } = params || {};
      let isLicenseTokenDisabled = false;
      return Promise.all([fetchDrmToken(params), fetchWaterMarkToken(params)])
        .then(([drmResponse, wmResponse, watchDuration]) => {
          isLicenseTokenDisabled = drmResponse?.disableLicenseToken;
          if (accessToken && !wmResponse?.filter && !drmResponse?.filter) {
            let extractedTrailerLaContentId;
            if (playTrailer || props?.contentType === V3_CONTENT_TYPE.TRAILER) {
              const licenseAcquisitionUrl =
                params?.contentMetaData?.playUrlDetail?.find(
                  (el) =>
                    el?.contentType === CONSTANTS.MediaURLAssetType.preview
                )?.licenseAcquisitionUrl?.widevine;
              const decryptLicenseAcquisitionUrl = decryptUrls(
                licenseAcquisitionUrl
              );
              if (decryptLicenseAcquisitionUrl) {
                const match =
                  decryptLicenseAcquisitionUrl.match(/contentId=([^&]+)/);
                extractedTrailerLaContentId = match ? match[1] : null;
              }
            }
            const body = getTokenBody(
              contentMetaData,
              true,
              extractedTrailerLaContentId
            );
            return getResolutionCapping(body, accessToken);
          } else {
            return {
              wmToken: wmResponse?.token,
              drmToken: drmResponse?.token,
              wmFilter: wmResponse?.filter,
              watchDuration: watchDuration,
              drmFilter: drmResponse?.filter,
              drmTokenExpTime: drmResponse?.expiresIn,
              wmTokenExpTime: wmResponse?.expiresIn,
            };
          }
        })
        .then((response) => {
          if (response?.contentID) {
            return {
              isLicenseTokenDisabled,
              wmToken: response?.token,
              drmToken: response?.token,
              wmFilter: response?.filter,
              drmFilter: response?.filter,
              wmTokenExpTime: response?.expiresIn,
              drmTokenExpTime: response?.expiresIn,
            };
          }
          return { ...response, isLicenseTokenDisabled };
        })
        .catch(errorCallBack);
    },
    [
      playTrailer,
      accessToken,
      getTokenBody,
      errorCallBack,
      fetchDrmToken,
      props?.contentType,
      fetchWaterMarkToken,
    ]
  );

  // common method to fetch the current and next content meta data
  const fetchContentHierarchyData = useCallback(
    ({ contentId, state }) => {
      return new Promise((resolve, reject) => {
        // New API integration for player title metadata
        dispatch(fetchSeriesMetaData({ contentId, state }))
          .then((data) => resolve(data))
          .catch(() => resolve({}));
      });
    },
    [dispatch]
  );

  const fetchContentInfo = useCallback(() => {
    const body = {
      contentId: props?.contentId,
      contentType: props?.contentType,
    };
    const successCallBack = (response, seriesMetaData) => {
      if (!response?.meta) {
        errorCallBack();
        return;
      }
      // WaterMark and Drm Handling Case 1: When user play the content directly from watch now button.
      fetchTokens({ contentMetaData: response?.meta ?? {} })
        .then(
          ({
            wmToken,
            drmToken,
            wmFilter,
            drmFilter,
            watchDuration,
            wmTokenExpTime,
            drmTokenExpTime,
            isLicenseTokenDisabled,
          }) => {
            let updatedResponse = JSON.parse(JSON.stringify(response?.meta));
            updatedResponse = {
              ...updatedResponse,
              wmToken,
              drmToken,
              wmFilter,
              drmFilter,
              watchDuration,
              wmTokenExpTime,
              drmTokenExpTime,
              seriesMetaData,
              isLicenseTokenDisabled,
            };
            contentData.current = updatedResponse;
            setContentMeta({});
          }
        )
        .catch(errorCallBack);
    };

    if (
      [V3_CONTENT_TYPE.MOVIE, V3_CONTENT_TYPE.TRAILER]?.includes(
        props?.contentType
      )
    ) {
      if (assetDetailsData?.[body?.contentId]) {
        successCallBack(
          JSON.parse(JSON.stringify(assetDetailsData?.[body?.contentId]))
        );
      } else {
        dispatch(fetchContentData({ ...body }))
          .then(successCallBack)
          .catch((error) =>
            errorCallBack(
              PLAYER_GENERIC_ERROR_CODE.FETCH_CONTENT_METADATA_FAIL,
              error
            )
          );
      }
    } else if (V3_CONTENT_TYPE.EPISODE === props?.contentType) {
      delete body.contentType;
      body.state = false;

      //Commented the code because the bug alc -2568
      // if (assetDetailsData?.[body?.contentId]) {
      //   successCallBack(
      //     JSON.parse(JSON.stringify(assetDetailsData?.[body?.contentId]))
      //   );
      // } else {

      /**
       * Fetches and save the episodic api data and heirarchy data
       */
      const contentHierarchyData = fetchContentHierarchyData({
        ...body,
        state: 'CURRENT',
      });

      const getCurentEpisodeData = new Promise((resolve, reject) => {
        dispatch(fetchEpisodeData({ ...body })).then(
          (res) => resolve(res),
          () => reject()
        );
      });

      Promise.all([getCurentEpisodeData, contentHierarchyData])
        .then(([episodeData, currentMetaData]) => {
          successCallBack(episodeData, currentMetaData);
        })
        .catch((error) =>
          errorCallBack(
            PLAYER_GENERIC_ERROR_CODE.FETCH_CONTENT_METADATA_FAIL,
            error
          )
        );
    } else if (
      [V3_CONTENT_TYPE.SERIES, V3_CONTENT_TYPE.SEASON]?.includes(
        props?.contentType
      )
    ) {
      body.state = false;
      dispatch(
        fetchFirstEpisode({ contentId: props?.contentId, saveInStore: true })
      )
        .then((firstEpisodeRes) => {
          fetchContentHierarchyData({
            contentId: firstEpisodeRes?.meta?.contentId,
            state: 'CURRENT',
          })
            .then((hierarchyMetaData) => {
              successCallBack(firstEpisodeRes, hierarchyMetaData);
            })
            .catch((error) =>
              errorCallBack(
                PLAYER_GENERIC_ERROR_CODE.FETCH_CONTENT_METADATA_FAIL,
                error
              )
            );
        })
        .catch((error) =>
          errorCallBack(
            PLAYER_GENERIC_ERROR_CODE.FETCH_CONTENT_METADATA_FAIL,
            error
          )
        );
    } else {
      /** TODO:
       * If the content type is a series, first get the recent watch history at the series level and extract the recently watched episode data. Save the watchDuration, then call fetchEpisodeData().
       */
      errorCallBack(PLAYER_GENERIC_ERROR_CODE.GENERIC_FAILURE);
    }
    getContentWatchHistory();
  }, [
    dispatch,
    fetchTokens,
    errorCallBack,
    setContentMeta,
    props?.contentId,
    assetDetailsData,
    props?.contentType,
    getContentWatchHistory,
    fetchContentHierarchyData,
  ]);

  /**
   * Set content metadata and fetch next episode data with its watch history.
   * @param {Object} params - Parameters for setting content metadata.
   * @param {string} params.switchToContent - if true, switch to content playback from trailer.
   * @param {string} params._nextContentData - if false, set the current episode data else set the next episode data.
   */
  const setContentMeta = useCallback(
    ({ _nextContentData = false, switchToContent = false }) => {
      let data = {};
      if (switchToContent) {
        data = JSON.parse(JSON.stringify(metaData));
        const { playbackUrl, ...rest } = data?.contentUrls;
        data.contentType = data?.originalContentType;
        data.playbackUrl = playbackUrl;
        data.drmCredentials = rest;
      } else {
        data = _nextContentData || prepareContentMeta(contentData.current);
      }

      if (data) {
        if (!_nextContentData && !switchToContent) {
          data.secondsOfContentWatched = props?.watchDuration || 0;
        }
        let isUrlInvalid = !checkVideoStreamUrlIsValid(data?.playbackUrl);
        control$?.error?.set?.(isUrlInvalid);
        const convivaInfo = getConvivaContentInfoVod(data, userData, {
          ...contentData.current,
          railID: 'NA',
          deviceId: device_id,
          accountType:
            activeSubscriptions === null
              ? CONVIVA_ACCOUNT_TYPE.guest
              : activeSubscriptions?.currentPlan?.isFreemium
                ? CONVIVA_ACCOUNT_TYPE.registerFree
                : CONVIVA_ACCOUNT_TYPE.registerVip,
          contentTitle: props?.contentTitle,
          subscriptionPlan: activeSubscriptions?.currentPlan?.description,
          providerContentTier:
            activeSubscriptions?.currentPlan?.serviceName || 'NA',
        });
        setConvivaContentInfo(convivaInfo);
        setMetaData(data);
        if (isUrlInvalid) {
          return;
        }
        if (switchToContent) {
          return;
        }

        if (data?.contentId && data?.contentType === V3_CONTENT_TYPE.EPISODE) {
          fetchNextEpisode(data?.contentId)
            .then(({ response, nextEpisodeHierarchyData }) => {
              if (!response) {
                // setMetaData({ ...data, isLastEpisode: true });
                nextContentData.current = false;
              } else {
                let updatedData = {
                  ...response?.meta,
                  seriesMetaData: nextEpisodeHierarchyData,
                };
                nextContentData.current = JSON.parse(
                  JSON.stringify(updatedData)
                );
                getContentWatchHistory(nextContentData.current);
                // setMetaData({
                //   ...data,
                //   isLastEpisode: false,
                //   disableLastEpisodeCTA: false,
                //   nextEpisodeMetaData: nextContentData.current,
                // });
              }
            })
            .catch(() => {
              if (!data?.isLastEpisode) {
                // setMetaData({ ...data, isLastEpisode: true });
              }
              nextContentData.current = false;
            });
        }
      } else {
        control$?.error?.set?.(true);
        // setMetaData(defaultMetaData);
      }
    },
    [
      userData,
      metaData,
      device_id,
      control$?.error,
      fetchNextEpisode,
      prepareContentMeta,
      props?.contentTitle,
      activeSubscriptions,
      props?.watchDuration,
      getContentWatchHistory,
    ]
  );

  /**
   * Prepare content metadata for playback
   * @param {Object} input - Input data for preparing content metadata.
   * @returns {Object} - Prepared content metadata.
   */
  const prepareContentMeta = useCallback(
    (input = {}) => {
      let data = {};
      data.skipInfo = { ...defaultSkipInfo };
      data.isVodPlayer = true;
      data.title = input?.title;
      data.defaultTitle = input?.defaultTitle;
      data.episodeNumber = input?.episodeNumber;
      data.seasonNumber = input?.seasonNumber;
      data.isLastEpisode = true;
      data.endTime = input?.endTime;
      data.ageRating = input?.rating;
      data.genreList = input?.genreList;
      data.contentId = input?.contentId;
      data.partnerId = input?.partnerId;
      data.contentType = input?.contentType;
      data.entitlements = input?.entitlements;
      data.posterUrl = input?.boxCoverImage ?? '';
      data.originalContentType = input?.contentType;
      data.episodeNumber = input?.episodeNumber;
      data.seasonNumber = input?.seasonNumber;
      data.seriesId = input?.seriesId;
      data.isOpen = input?.seriesMetaData?.series?.isOpen;
      data.episodeSortOrder = input?.episodeSortOrder;
      data.seasonSortOrder = input?.seasonSortOrder;
      data.dmData = input?.dmData;
      data.scrubbingVttFile = input?.scrubbingVttFile;
      // data.isContentExpired = input?.contentExpiryDate
      //   ? +new Date() >= +new Date(input?.contentExpiryDate)
      //   : false;
      data.isContentExpired = false;
      // data.isContentExpired = input?.contentExpiryDate
      //   ? +new Date() >= +new Date(input?.contentExpiryDate)
      //   : false;

      if (input?.genreList) {
        let { secondary } = flattenGenres(input?.genreList);
        data.genre = secondary ?? [];
      }

      const { trailerUrls = {}, contentUrls = {} } = flatContentPlayUrls(
        input?.playUrlDetail,
        props?.drmCredentials?.certificateUrl
      );

      data.contentUrls = contentUrls;
      data.trailerUrls = trailerUrls;

      if (playTrailer) {
        const { format, playbackUrl, ...rest } = trailerUrls;
        data.playbackUrl = playbackUrl;
        data.drmCredentials = rest;
        data.contentMaxResolution = format;
        data.contentType = V3_CONTENT_TYPE.TRAILER;
        data.isCSAIAdsEnabled = false;
      } else {
        const { format, playbackUrl, ...rest } = contentUrls;
        data.playbackUrl = playbackUrl;
        data.contentMaxResolution = format;
        data.isCSAIAdsEnabled =
          input?.purchaseDetails?.[0]?.adsEnabled ?? false;

        // if (utils?.isIOS && input?.wmToken) {
        //   data.playbackUrl = appendTokenToUrl(
        //     data.playbackUrl,
        //     input?.contentId,
        //     input?.wmToken
        //   );
        // }
        data.drmCredentials = rest;
      }
      data.isWMEnabled =
        (contentData?.current?.isCDNAuthEnabled ||
          contentData?.current?.isWMAuthEnabled) &&
        isWaterMarkTokenRequired;
      data.wmToken = input.wmToken;
      data.drmToken = input.drmToken;
      data.wmTokenExpTime = input.wmTokenExpTime;
      data.drmTokenExpTime = input.drmTokenExpTime;
      data.isDrmEnabled = !input?.isLicenseTokenDisabled;
      data.wmBasedMaxResolution =
        input?.drmFilter ||
        input?.wmFilter ||
        (accessToken
          ? resolutionConfig?.maxResolution
          : resolutionConfig?.freeMaxResolution);
      data.cappedResolution = getUserEntitlementBasedMaxResolution(
        accessToken,
        data.wmBasedMaxResolution,
        contentData?.current?.isWMAuthEnabled,
        contentData?.current?.isCDNAuthEnabled,
        resolutionConfig
      );
      data.platformResolutionConfig = {
        maxResolution: resolutionConfig?.maxResolution,
        minResolution: resolutionConfig?.minResolution,
        userResolutionConfig: resolutionConfig?.resolutions,
        freeMaxResolution: resolutionConfig?.freeMaxResolution,
      };
      data.drmCredentials.drm = true; //TODO: It has to be dynamic
      data.duration = data.contentUrls?.duration;
      data.activeSubscription = userEntitlementInfoData;

      if (input.contentType === V3_CONTENT_TYPE.EPISODE) {
        const { episode, series, season } = input?.seriesMetaData || {};
        data.isLastEpisode = false;
        data.disableLastEpisodeCTA = true;
        if (!playTrailer) {
          //        let contentDetails;
          //                  if (input?.seriesId === null && input?.seasonId === null) {
          //                    data.title = `${input?.title ?? input?.defaultTitle} ${
          //                      input?.episodeNumber ? `E${input?.episodeNumber}` : ''
          //                    }`;
          //                  } else if (!input?.seasonId) {
          //                    contentDetails = {
          //                      title: episode?.title ?? episode?.defaultTitle,
          //                      isShowTitle: true,
          //                      seriesTitle: series?.title,
          //                      defaultTitle: series?.title ?? series?.defaultTitle,
          //                      episodeNumber: episode?.episodeNumber,
          //                    };
          //                    data.title = getOpenSeriesWatchedTitle(contentDetails);
          //                  } else {
          //                    contentDetails = {
          //                      defaultTitle: series?.title ?? series?.defaultTitle,
          //                      seasonNumber: episode?.seasonNumber,
          //                      episodeNumber: episode?.episodeNumber,
          //                      seasonTitle: season?.title,
          //                      episodicTitle: episode?.title,
          //                      seriesTitle: series?.title ?? series?.defaultTitle,
          //                      isShowTitle: true,
          //                    };
          //                    data.title = getClosedSeriesWatchedTitle(contentDetails);
          if (input?.isOpen) {
            data.title = data?.title;
          } else {
            data.title = data?.title;
          }
        }
      }

      data.skipInfo = prepareSkipInfo(input, data.contentUrls?.duration);

      if (
        ![
          V3_CONTENT_TYPE.MOVIE,
          V3_CONTENT_TYPE.TRAILER,
          V3_CONTENT_TYPE.EPISODE,
        ]?.includes(input.contentType)
      ) {
        return false;
      }
      data.posterUrl = addImagePrefixUrl(data.posterUrl);
      data.posterUrl = `https://mediaready.videoready.int.xp.irdeto.com/astro/image/fetch/${data.posterUrl}`;
      return data;
    },
    [
      playTrailer,
      accessToken,
      resolutionConfig,
      props?.drmCredentials,
      userEntitlementInfoData,
    ]
  );

  /**
   * Get content watch history
   * @param {Object} nextContent - Next content data.
   */
  const getContentWatchHistory = useCallback(
    (nextContent = false) => {
      return new Promise((resolve, reject) => {
        if (!accessToken || playTrailer) {
          return;
        }

        const params = {
          contentType: 'VOD',
          contentId: nextContent ? nextContent?.contentId : props?.contentId,
        };

        if (
          props?.watchHistory?.contentId === params.contentId &&
          props?.watchHistory?.watchDuration &&
          !nextContent
        ) {
          setSecondsOfContentWatched(props?.watchHistory?.watchDuration);
          resolve(props?.watchHistory?.watchDuration);
        } else if (fromViewAll) {
          setSecondsOfContentWatched(props?.watchHistory?.watchDuration);
        } else {
          getMovieContinueWatchByContentIdMobile(accessToken, params)
            .then((response) => {
              let data = parseMovieWatchHistoryData(response);
              nextContentData.current = {
                ...nextContentData.current,
                secondsOfContentWatched: data,
              };

              resolve(data);
            })
            .catch((error) => resolve(0));
        }
      });
    },
    [accessToken, props?.contentId, props?.watchHistory]
  );

  /**
   * Fetch next episode data based on content id.
   * @param {string} contentId - Content id.
   * @returns {Promise} - Promise resolving to next episode data.
   */
  const fetchNextEpisode = useCallback(
    async (contentId) => {
      // Call this method after setting the contentMetaData
      const body = {
        state: 'NEXT',
        contentId: contentId,
      };
      const episodePromise = new Promise((resolve, reject) => {
        dispatch(fetchEpisodeData({ ...body }))
          .then((response) => {
            if (response?.meta) {
              resolve(response);
            } else {
              reject(false);
            }
          })
          .catch((error) => {
            reject(false);
          });
      });
      const nextContentHierarchyData = fetchContentHierarchyData(body);
      return Promise.all([episodePromise, nextContentHierarchyData]).then(
        ([response, nextEpisodeHierarchyData]) => {
          return {
            response,
            nextEpisodeHierarchyData,
          };
        }
      );
    },
    [dispatch, fetchContentHierarchyData]
  );

  /**
   * Sets the next episode data from the nextContentData ref into the metaData state.
   * @param {function} callBack - A callback function to execute after setting the next content data.
   */
  const playNextContent = useCallback(
    (callBack = () => {}) => {
      if (nextContentData.current === null) {
        return;
      }
      callRemoveLastContentFromWatchHistory.current = true;
      // WaterMark and Drm Handling Case 2: When user play the next content from player.

      fetchTokens({ contentMetaData: nextContentData.current ?? {} })
        .then(
          ({
            drmToken,
            wmToken,
            wmFilter,
            drmFilter,
            drmTokenExpTime,
            wmTokenExpTime,
          }) => {
            let updatedResponse = JSON.parse(
              JSON.stringify(nextContentData.current)
            );
            updatedResponse = {
              ...updatedResponse,
              drmToken,
              wmToken,
              wmFilter,
              drmFilter,
              drmTokenExpTime,
              wmTokenExpTime,
            };
            contentData.current = updatedResponse;
            setContentMeta({});
            setSecondsOfContentWatched(
              nextContentData.current?.secondsOfContentWatched
            );

            nextContentData.current = null;
            callBack();
          }
        )
        .catch(errorCallBack);
    },
    [fetchTokens, errorCallBack, setContentMeta]
  );

  /**
   * To check next episode entitlements during binge watch.
   * @returns {boolean} - boolean value based in content entitlements.
   */
  const getNextContentInfo = useCallback(() => {
    let nextContentInfo = {
      id: '',
      title: '',
      entitlements: [],
      isContentEntitled: false,
    };
    if (
      userEntitlementInfoData &&
      Object.keys(userEntitlementInfoData).length &&
      nextContentData.current?.entitlements?.length
    ) {
      nextContentInfo.isContentEntitled = checkForContentEntitlement(
        nextContentData.current?.entitlements,
        userEntitlementInfoData
      );
    }
    nextContentInfo.id = nextContentData.current?.contentId;
    nextContentInfo.title = nextContentData.current?.title;
    nextContentInfo.entitlements = nextContentData.current?.entitlements;
    return nextContentInfo;
  }, [userEntitlementInfoData]);

  /**
   * Sends a heartbeat update for the current playback state.
   * This updates the server with the current playback progress.
   * @param {number} currentTime - The current playback time in seconds.
   * @param {number} duration - The total duration of the content in seconds.
   */
  const callHeartBeatFunction = useCallback(
    (currentTime = 0, duration = 0) => {
      if (
        playTrailer ||
        contentData?.current?.contentType === V3_CONTENT_TYPE.TRAILER
      ) {
        return;
      }
      if (!accessToken || currentTime === null || !(currentTime > 0)) {
        return;
      }

      const params = {
        contentType: 'VOD',
        contentId: contentData?.current?.contentId,
        subscriberId: userData?.subscriber?.customerId,
        watchDuration: parseInt(currentTime, 10),
      };
      const contentWatched = currentTime / duration;

      if (
        parseInt(currentTime, 10) >= cwStartThreshold &&
        contentWatched < cwEndThreshold
      ) {
        dispatch(
          updateContinueWatchData(
            accessToken,
            params,
            userData?.subscriber?.profileId
          )
        );
        return;
      }
      if (contentWatched >= cwEndThreshold) {
        let removeParams = {
          contentType: 'VOD',
          contentId: contentData?.current?.contentId,
        };
        dispatch(
          removeContentFromContinueWatching({ accessToken, ...removeParams })
        );

        if (contentData.current.contentType !== V3_CONTENT_TYPE.EPISODE) {
          return;
        }

        // Explicitly setting the next episode data in watch history.
        if (nextContentData.current !== null) {
          const { isContentEntitled = false } = getNextContentInfo();
          if (isContentEntitled) {
            params.watchDuration = 0;
            params.contentId = nextContentData.current?.contentId;
            dispatch(
              updateContinueWatchData(
                accessToken,
                params,
                userData?.subscriber?.profileId
              )
            );
          }
        }
        return;
      }
    },
    [
      dispatch,
      accessToken,
      playTrailer,
      cwEndThreshold,
      cwStartThreshold,
      getNextContentInfo,
      userData?.subscriber?.profileId,
      userData?.subscriber?.customerId,
    ]
  );

  /**
   * Removes the last content from the continue watching list.
   * This function is called when the user has finished watching the content.
   */
  const removeLastContentFromWatchHistory = useCallback(() => {
    if (callRemoveLastContentFromWatchHistory.current) {
      let params = {
        contentType: 'VOD',
        contentId: metaData?.contentId,
      };
      dispatch(removeContentFromContinueWatching({ accessToken, ...params }));
      callRemoveLastContentFromWatchHistory.current = false;
    }
  }, [accessToken, dispatch, metaData?.contentId]);

  function toggleTrailerToContent(value) {
    setPlayTrailer(value);
    // WaterMark and Drm Handling Case 3: When trailer is over, play the next content.
    fetchTokens({ contentMetaData: contentData.current ?? {} })
      .then(
        ({
          wmToken,
          wmFilter,
          drmToken,
          drmFilter,
          wmTokenExpTime,
          drmTokenExpTime,
        }) => {
          let updatedResponse = JSON.parse(JSON.stringify(contentData.current));
          updatedResponse = {
            ...updatedResponse,
            switchToContent: true,
            drmToken,
            wmToken,
            wmFilter,
            drmFilter,
            drmTokenExpTime,
            wmTokenExpTime,
          };
          contentData.current = updatedResponse;
          setContentMeta({});
        }
      )
      .catch(errorCallBack);
  }

  useEffect(() => {
    // fetchContentInfo();
    fetchContentInfoFromLocal();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [props?.contentId]);

  const fetchContentInfoFromLocal = useCallback(async () => {
    const downloadItem =
      await DownloadManager.getDownloadContentFromLocalStorage(
        props?.downloadId,
        true
      );

    if (downloadItem) {
      contentData.current = downloadItem;
      setContentMeta({});
      //   setMetaData(downloadItem);
    }
  }, [props?.downloadId, setContentMeta]);

  return {
    metaData,
    accessToken,
    playTrailer,
    cwEndThreshold,
    playNextContent,
    cwPollingInterval,
    convivaContentInfo,
    getNextContentInfo,
    fetchWaterMarkToken,
    callHeartBeatFunction,
    toggleTrailerToContent,
    secondsOfContentWatched,
    prepareNextContentData: () => {},
    removeLastContentFromWatchHistory,
  };
}

export default usePrepareOfflinePlaybackVod;
