import React, {
  memo,
  useRef,
  useMemo,
  useState,
  useEffect,
  useCallback,
} from 'react';
import { View, Pressable, Platform, StatusBar } from 'react-native';

import Animated, {
  runOnJS,
  withTiming,
  useSharedValue,
  useAnimatedStyle,
} from 'react-native-reanimated';
import {
  Show,
  useObservable,
  useObserveEffect,
  useSelector as legendappUseSelector,
} from '@legendapp/state/react';
import { useSelector } from 'react-redux';
import { debounce, throttle } from 'lodash';
import { useTranslation } from 'react-i18next';
import LinearGradient from 'react-native-linear-gradient';
import { useRemoteMediaClient } from 'react-native-google-cast';
import { CacheManager } from '@georstat/react-native-image-cache';
import { useDispatch } from 'react-redux';

import {
  sortResolution,
  generateVastTag,
  fetchHLSPlaylist,
  fetchMPDPlaylist,
  extractErrorInfo,
  mediaWatchedEvent,
  getValidResolutions,
  extractAdsErrorNumber,
  logPlayerErrorToSentry,
  sortResolutionByHeight,
  sortResolutionBandwidth,
  showContentExpiredError,
  showGeneralPlaybackError,
  logPlayerAdsErrorToSentry,
  removeDuplicateResolutions,
  showContentNotAvailablePopUp,
  showConcurrentStreamErrorPopUp,
  registerGaVideoWatchedPercentage,
  showLicenseDeniedNoEntitlementPopUp,
  convivaErrorBifurcation,
  showModalObject,
} from '../helper';
import {
  AD_EVENTS,
  SAFE_EDGES,
  PLAYER_MODE,
  PLAYER_STATE,
  AD_ERROR_TYPE,
  PLAYER_MESSAGES,
  DRM_ERROR_CODES,
  ADS_ERROR_CODES,
  PLAYER_SKIP_MODE,
  PLAYER_MODAL_TYPES,
  PLAYER_RESIZE_MODE,
  PLAYER_FEATURE_CONFIG,
  PLAYER_RELOAD_ERROR_TYPE,
  PLAYER_GENERIC_ERROR_CODE,
  WATERMARK_REFRESH_TOKEN_TIME,
} from '../playerConstants';
import VideoPlayer from '../Core';
import {
  reportAdError,
  reportAdEnded,
  reportAdBitrate,
  reportAdSkipped,
  reportAdStarted,
  getConvivaAdInfo,
  reportAdBreakEnded,
  reportPlaybackError,
  reportAdBreakStarted,
  reportAppBackgrounded,
  reportAppForegrounded,
  reportAdPlayPausedState,
} from 'mobile/src/utils/convivaMethods';
import PlayerOverlay from '../PlayerOverlay';
import AdsControl from '../components/AdControl';
import SkipInfoView from '../components/SkipInfo';
import Loader from 'mobile/src/components/Loader';
import { COLORS } from '@video-ready/common/utils';
import PlayerModal from '../components/PlayerModal';
import utils, { APP_STATE } from 'mobile/src/utils';
import {
  videoPlayEventFB,
  videoPlayEndEventFB,
  videoFirstQuartilePlayEventFB,
  videoThirdQuartilePlayEventFB,
  videoSecondQuartilePlayEventFB,
} from 'mobile/src/utils/facebookCustomEventLogger';
import { normalize } from 'mobile/src/styles/Mixins';
import InitialLoader from '../components/InitialLoader';
import appConfig from '@video-ready/common/utils/config';
import Routenames from 'mobile/src/navigation/routeName';
import useLockStatus from 'mobile/src/hooks/useLockStatus';
import CONSTANTS from '@video-ready/common/utils/constant';
import useAppStateListener from 'mobile/src/hooks/useAppState';
import {
  GA_KEYS,
  GA_VALUES,
  GA_EVENT_NAMES,
} from '@video-ready/common/constants/GoogleAnalyticsConstants';
import { useSafeHeights } from 'mobile/src/hooks/useSafeHeights';
import CommonModal from 'mobile/src/components/Modal/CommonModal';
import PlayerOverlayOnCast from '../components/PlayerOverlayOnCast';
import { requestInAppReview } from 'mobile/src/utils/commonMethods';
import { CONVIVA_PLAYER_NAME } from 'mobile/src/utils/convivaMethods';
import { V3_CONTENT_TYPE, VIDEO_TYPE } from 'mobile/src/appConstants';
import { logBreadcrumb } from '@video-ready/common/utils/sentryMobile';
import { videoPlayers } from '@video-ready/common/constants/VideoPlayer';
import { applyCustomToast, ToastType } from 'mobile/src/components/Toast';
import { registerAnalyticsEvent } from 'mobile/src/utils/AnalyticsMethods';
import { isIndependentTvShow } from 'mobile/src/screens/ContentPage/helper';
import styles, { DEFAULT_HIDE_OVERLAY_TIME, overlayColors } from '../styles';
import {
  fetchViewAllEpisode,
  getLastEpisodePlaybackStatus,
} from '@video-ready/common/services/home/<USER>';
import NetworkStats from 'mobile/src/screens/NetworkStats';
import {
  State,
  PinchGestureHandler,
  GestureHandlerRootView,
} from 'react-native-gesture-handler';
import {
  getSeasonListQuery,
  getViewAllEpisodeData,
} from '../components/PlayerModal/EpisodesModal/helper';
import usePrepareOfflinePlaybackVod from '../hooks/usePrepareOfflinePlaybackVod';
const { MOBILE_BREADCRUMB_CATEGORIES } = CONSTANTS?.SENTRY_CONSTANTS;
const category = MOBILE_BREADCRUMB_CATEGORIES.PLAYER;

const defaultCurrentSkipInfo = {
  skipFor: false,
  endTime: undefined,
  startTime: undefined,
};

const AnimatedPressable = Animated.createAnimatedComponent(Pressable);

function OfflineVodPlayer({
  railId,
  videoType,
  contentId,
  navigation,
  contentData,
  contentTitle,
  bufferConfig,
  whileRotateSV,
  fromHeroBanner,
  goBack = () => {},
  videoDimensions = {},
  changeOrientation = () => {},
  seriesData,
  ...props
}) {
  const thirtyMinutes = 30 * 60;
  // const isOnline = useNetworkState();
  const lockStatus = useLockStatus();
  const appState = useAppStateListener();
  const { safeEdges } = useSafeHeights();
  const castClient = useRemoteMediaClient();
  const { t: translate } = useTranslation();

  const dispatch = useDispatch();

  const onSeekValueRef = useRef(0);
  const adClickRef = useRef();
  const metaDataRef = useRef({});
  const timeoutId = useRef(null);
  const currentTimeRef = useRef(0);
  const playbackRateRef = useRef(0);
  const PlayerEventRefs = useRef({});
  const totalDurationRef = useRef(0);
  const videoQuartileRef = useRef({});
  const wmTokenExpTime = useRef(null);
  const isAdPlayingRef = useRef(false);
  const videoResolutionsRef = useRef();
  const preRollAdsPlayed = useRef(false);
  const adBreakStartedRef = useRef(false);
  const lastSelectedVideoTrackRef = useRef(); // Will be used in case of episode or series binge watch.
  const hideControlsTimeOutId = useRef(null);
  const disablePlayerReloading = useRef(false);
  const currentSkipInfo = useRef(defaultCurrentSkipInfo);
  const prevResizeMode = useRef(PLAYER_RESIZE_MODE.CONTAIN);
  const [firstFrameLoaded, setFirstFrameLoaded] = useState(false);
  const playbackRetryCount = useRef(0);
  const isWatchCreditClicked = useRef(false);
  const player = useRef(
    new VideoPlayer({
      playerName: videoPlayers.RNVIDEO,
    })
  ).current;
  const videoRef = useRef(null);
  const [scale, setScale] = useState(1);
  const [wmToken, setWmToken] = useState(null);
  const [onLoadFlag, setOnLoadFlag] = useState(false);
  const [videoPlayerKey, setVideoPlayerKey] = useState(0);
  const [nextEpisodeLoader, setNextEpisodeLoader] = useState(false);
  const [showSlowNetworkToast, setShowSlowNetworkToast] = useState(false);
  const [controlsHide, setControlsHide] = useState(true);
  const [viewAllData, setViewall] = useState(null);

  const playerVolume = useSharedValue(0);
  const initialLoader = useSharedValue(1);
  const hideAllOverlay = useSharedValue(false);
  const overlayVisibilityValue = useSharedValue(0);

  const control$ = useObservable(props.control$);

  const userData = useSelector((state) => state?.auth?.signInResponse?.data);
  const dictionary = useSelector((state) => state?.miscellaneous?.dictionary);

  const seriesList = useSelector((state) => state?.home?.seriesList);

  const preferredForwardBufferDuration =
    appConfig?.getPreferredForwardBufferDuration();

  const SCALECONSTANT = {
    addSacle: 0.1,
  };

  const {
    metaData,
    playTrailer,
    accessToken,
    cwEndThreshold,
    playNextContent,
    cwPollingInterval,
    convivaContentInfo,
    getNextContentInfo,
    callHeartBeatFunction,
    toggleTrailerToContent,
    secondsOfContentWatched,
    removeLastContentFromWatchHistory,
    fetchWaterMarkToken,
  } = usePrepareOfflinePlaybackVod({
    control$,
    contentId,
    contentData,
    contentTitle,
    ...props,
  });

  metaDataRef.current = metaData;
  /* @legendapp/state */
  const posterResizeMode = legendappUseSelector(() =>
    control$?.posterResizeMode.get()
  );

  const airplayConnected = legendappUseSelector(() =>
    control$.airplayConnected.get()
  );
  const playInBackground = legendappUseSelector(() =>
    control$.playInBackground.get()
  );
  const pictureInPicture = legendappUseSelector(() =>
    control$.pictureInPicture.get()
  );
  const isReloadingPlayer = legendappUseSelector(() =>
    control$.isReloadingPlayer.get()
  );
  const selectedTextTrack = legendappUseSelector(() =>
    control$.selectedTextTrack.get()
  );
  const selectedAudioTrack = legendappUseSelector(() =>
    control$.selectedAudioTrack.get()
  );
  const googleCastConnected = legendappUseSelector(() =>
    control$.googleCastConnected.get()
  );
  const selectedVideoTrack = legendappUseSelector(() =>
    control$.selectedVideoTrack.get()
  );
  const isPlayerPauseState = legendappUseSelector(() =>
    control$.isPaused.get()
  );
  const selectedPlaybackSpeed = legendappUseSelector(() =>
    control$.selectedPlaybackSpeed.get()
  );
  const selectedModal = legendappUseSelector(() =>
    control$.selectedModal.get()
  );
  const enableConvivaVideoAnalytics = legendappUseSelector(() =>
    control$.enableConvivaVideoAnalytics.get()
  );
  const error = legendappUseSelector(() => control$.error.get());
  const isMuted = legendappUseSelector(() => control$?.isMuted?.get());
  const resizeMode = legendappUseSelector(() => control$.resizeMode.get());
  const fullScreen = legendappUseSelector(() => control$.fullScreen.get());
  const playerMode = legendappUseSelector(() =>
    control$.fullScreen.get() ? PLAYER_MODE.LANDSCAPE : PLAYER_MODE.PORTRAIT
  );
  const isBuffering = legendappUseSelector(() => control$.isBuffering.get());
  const playerState = legendappUseSelector(() => control$.playerState.get());
  const skipInfoMode = legendappUseSelector(() => control$.skipInfoMode.get());
  const skipInfo = metaData?.skipInfo;

  /* Slow network config value started */
  const enableSlowNetworkFeature = legendappUseSelector(() =>
    control$.enableSlowNetworkFeature.get()
  );
  const networkThresholdTimeout = legendappUseSelector(() =>
    control$.networkThresholdTimeout.get()
  );
  const slowNetworkToastThreshold = legendappUseSelector(() =>
    control$.slowNetworkToastThreshold.get()
  );
  /* Slow network config value ended */

  const bufferingStartTimeRef = useRef(0);

  const maxRetryCount = appConfig?.getPlayerConfig()?.maxRetry;
  const playerReloadErrorType =
    appConfig?.getPlayerConfig()?.retryErrorCodes || PLAYER_RELOAD_ERROR_TYPE;

  const isDefaultPlayerModeLandscape = useMemo(() => {
    return props?.defaultPlayerMode === PLAYER_MODE.LANDSCAPE;
  }, [props?.defaultPlayerMode]);

  const adTagUrl = useMemo(
    () =>
      generateVastTag({
        userData,
        metaData,
        selectedAudioTrack,
        secondsOfContentWatched,
      }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [userData, metaData, secondsOfContentWatched]
  );

  const triggerMediaEndEvents = useCallback(() => {
    mediaWatchedEvent(
      metaDataRef.current,
      null,
      selectedAudioTrack,
      selectedTextTrack,
      currentTimeRef.current
    );
  }, [selectedAudioTrack, selectedTextTrack]);

  const unmountPlayer = useMemo(() => {
    if (
      nextEpisodeLoader ||
      googleCastConnected ||
      playerState === PLAYER_STATE.STOP
    ) {
      return true;
    } else {
      return false;
    }
  }, [playerState, nextEpisodeLoader, googleCastConnected]);

  useEffect(() => {
    const seasonsData = getSeasonListQuery(seriesList);
    const scrollingDirection = CONSTANTS.VIEWALL.PREV_NEXT;
    getViewAllEpisodeData(metaData, scrollingDirection, seasonsData).then(
      (res) => {
        setViewall(res);
      }
    );
  }, [
    metaData?.seriesId,
    metaData?.contentId,
    metaData?.seasonSortOrder,
    metaData?.episodeSortOrder,
  ]);

  useEffect(() => {
    if (metaData?.title) {
      registerAnalyticsEvent(GA_EVENT_NAMES.SCREEN_VIEW, {
        [GA_KEYS.SCREEN_CLASS]: GA_VALUES.MAIN_ACTIVITY,
        [GA_KEYS.SCREEN_NAME]: `Player - ${metaData?.title}`,
      });
    }
  }, [metaData?.title]);

  useEffect(() => {
    navigation.addListener('beforeRemove', (e) => {
      // when user go back using gesture ios
      player?.destroyPlayer?.();
    });
    return () => {
      // CacheManager.clearCache(); // TODO commented out for now.
      if (adBreakStartedRef.current) {
        reportAdEnded();
        reportAdBreakEnded();
      }
      registerAnalyticsEvent(GA_EVENT_NAMES.PLAYER_CLOSE_FULL_SCREEN_EVENT, {
        [GA_KEYS.EVENT_CATEGORY]: CONVIVA_PLAYER_NAME,
        [GA_KEYS.EVENT_LABEL]: metaDataRef.current?.title,
        [GA_KEYS.VIDEO_TITLE]: metaDataRef.current?.title,
        [GA_KEYS.VIDEO_ID]: metaDataRef.current?.contentId,
        [GA_KEYS.EVENT_ACTION]: GA_VALUES.PLAYER_CLOSE_FULL_SCREEN_EVENT_ACTION,
      });
    };
  }, []);

  useEffect(() => {
    if (!isAdPlayingRef.current) {
      prevResizeMode.current = resizeMode;
    }
  }, [resizeMode]);

  useEffect(() => {
    if (utils.isTab && utils.isIOS) {
      StatusBar.setHidden(true, false);
      return () => {
        StatusBar.setHidden(false, false);
      };
    }
  }, []);

  useEffect(() => {
    wmTokenExpTime.current = metaData?.wmTokenExpTime;
    setWmToken(metaData?.wmToken);

    return () => {
      // Clean up intervals and timeouts on unmount
      removeWmTokenTimeOut();
    };
  }, [metaData]);

  useEffect(() => {
    if (metaData?.playbackUrl) {
      control$?.isReloadingPlayer?.set(false);
    }
  }, [metaData?.playbackUrl]);

  // useEffect(() => {
  //   if (metaData?.playbackUrl) {
  //     if (utils.isAndroid) {
  //       videoResolutionsRef.current = null;
  //     }
  //     if (metaData?.isWMEnabled) {
  //       // Only call if isWMEnabled is true and wmToken is available
  //       if (wmToken) {
  //         getStreamResolutions(metaData?.playbackUrl, true, wmToken);
  //       }
  //     } else {
  //       // Call directly if isWMEnabled is false
  //       getStreamResolutions(metaData?.playbackUrl, false);
  //     }
  //   }
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [metaData?.playbackUrl, metaData?.isWMEnabled, wmToken]);

  const getStreamResolutions = useCallback(
    (playbackUrl, isWMEnabled, _wmToken) => {
      if (utils.isIOS) {
        fetchHLSPlaylist(playbackUrl, isWMEnabled, _wmToken).then(
          (videoResolutions) => {
            if (videoResolutions) {
              videoResolutionsRef.current = videoResolutions;
              if (!isAdPlayingRef.current) {
                requestAnimationFrame(() => {
                  performVideoResolutionOperation(false);
                });
              }
            }
          }
        );
      } else {
        fetchMPDPlaylist(playbackUrl, isWMEnabled, _wmToken)
          .then((videoResolutions) => {
            return Object.values(videoResolutions);
          })
          .then((videoResolutions) => {
            const updatedResolutions = videoResolutionsRef.current?.length
              ? [...videoResolutionsRef.current, ...videoResolutions]
              : videoResolutions || [];
            const uniqueResolutions =
              removeDuplicateResolutions(updatedResolutions);
            videoResolutionsRef.current =
              uniqueResolutions && Object.keys(uniqueResolutions)?.length
                ? sortResolution(Object.values(uniqueResolutions))
                : [];
            // performVideoResolutionOperation(false);
          });
      }
    },
    []
  );

  // restrict picture in picture when user is connected to googlecast or airplay
  const enablePictureInPicture = useMemo(() => {
    if (airplayConnected || googleCastConnected || isPlayerPauseState) {
      return false;
    } else {
      return pictureInPicture;
    }
  }, [
    airplayConnected,
    googleCastConnected,
    isPlayerPauseState,
    pictureInPicture,
  ]);

  useEffect(() => {
    // TODO: Need to handle errors for Subtitle Errors, Audio Errors - https://irdeto.atlassian.net/browse/ASTF-760
    if (error) {
      initialLoader.value = 0;
      overlayVisibilityValue.value = 0;
      // control$?.error?.set?.(true);
      // control$?.selectedModal?.set?.(PLAYER_MODAL_TYPES.ERROR);
      const { title, entitlements } = metaDataRef.current || {};
      let errorMessage = '';
      if (metaData?.isContentExpired) {
        errorMessage = showContentExpiredError(
          false,
          title,
          navigation,
          dictionary,
          translate,
          true
        );
      } else if (error === DRM_ERROR_CODES.CONTENT_NOT_AVAILABLE_AT_LOCATION) {
        errorMessage = showContentNotAvailablePopUp(
          error,
          navigation,
          dictionary,
          translate,
          true
        );
      } else if (error === DRM_ERROR_CODES.LICENSE_DENIED_NO_ENTITLEMENT) {
        errorMessage = showLicenseDeniedNoEntitlementPopUp(
          error,
          title,
          entitlements,
          navigation,
          dictionary,
          translate,
          true,
          () => {},
          control$,
          false,
          metaData
        );
      } else if (
        error === DRM_ERROR_CODES.LICENSE_DENIED_MAX_STREAMS ||
        error === DRM_ERROR_CODES.TOO_MANY_CONCURRENT_STREAMS ||
        error === DRM_ERROR_CODES.TOO_MANY_CONCURRENT_STREAMS_FOR_DEVICE_TYPE
      ) {
        errorMessage = showConcurrentStreamErrorPopUp(
          error,
          title,
          entitlements,
          navigation,
          dictionary,
          translate,
          true,
          () => {},
          control$,
          false,
          metaData
        );
      } else {
        showGeneralPlaybackError(
          false,
          control$,
          navigation,
          dictionary,
          translate,
          true,
          () => {},
          error
        );
      }
      //Error Bifurcation
      errorMessage = convivaErrorBifurcation(error, errorMessage);
      console.log('Error Bifurcation', errorMessage);
      if (errorMessage) {
        /**
         * Hot Fix:
         * Delaying error reporting if ads are playing before the player throws an error.
         * If the player error is reported before the Conviva `ad.podEnd` event,
         * Conviva will not log the playback error on the dashboard.
         * */
        if (utils?.isAndroid && isAdPlayingRef.current) {
          setTimeout(() => {
            reportPlaybackError(errorMessage);
          }, 750);
        } else {
          reportPlaybackError(errorMessage);
        }
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [error, metaData?.isContentExpired, navigation, dictionary, translate]);

  useEffect(() => {
    //when user lock the screen for android
    if (utils.isAndroid) {
      if (lockStatus && !googleCastConnected) {
        control$.isPaused.set(true);
      } else {
        control$.isPaused.set(false);
      }
    }
  }, [control$.isPaused, googleCastConnected, lockStatus]);

  /* To Maintain Player State */
  useObserveEffect(control$.isPaused, ({ value }) => {
    if (playerState === PLAYER_STATE.STOP) {
      control$?.isBuffering?.set?.(false);
      return;
    }
    if (value) {
      // clearOverlayTimer();
      control$?.playerState?.set?.(PLAYER_STATE.PAUSED);
    } else {
      control$?.playerState?.set?.(PLAYER_STATE.PLAYING);
    }
  });

  useObserveEffect(control$.selectedModal, ({ value }) => {
    if (
      PLAYER_MODAL_TYPES[value] !== PLAYER_MODAL_TYPES.NONE &&
      PLAYER_MODAL_TYPES[value] !== PLAYER_MODAL_TYPES.VIDEO_TRACK &&
      PLAYER_MODAL_TYPES[value] !== PLAYER_MODAL_TYPES.VIDEO_TRACK_DEFAULT &&
      PLAYER_MODAL_TYPES[value] !== PLAYER_MODAL_TYPES.PLAYBACK_SPEED &&
      PLAYER_MODAL_TYPES[value] !== PLAYER_MODAL_TYPES.AUDIO_AND_TEXT_TRACK &&
      PLAYER_MODAL_TYPES[value] !== PLAYER_MODAL_TYPES.EPISODE_LIST
    ) {
      overlayVisibilityValue.value = 0;
    } else if (error) {
      overlayVisibilityValue.value = 1;
    }
  });

  // To collect Sentry Breadcrumb for Exit/Enter FullScreen event
  useObserveEffect(control$.fullScreen, ({ value }) => {
    logBreadcrumb(
      category,
      value ? PLAYER_MESSAGES.FULLSCREEN_ENTER : PLAYER_MESSAGES.FULLSCREEN_EXIT
    );
  });

  const playerOverlayWrapper = useAnimatedStyle(() => {
    return {
      opacity: withTiming(hideAllOverlay?.value ? 0 : 1, { duration: 100 }),
      zIndex: withTiming(hideAllOverlay?.value ? -3 : 1, { duration: 100 }),
    };
  }, [hideAllOverlay.value]);

  const overlayVisibilityStyle = useAnimatedStyle(() => {
    let overlayVisibility = overlayVisibilityValue.value;
    if (whileRotateSV?.value != null) {
      /* For Rotation */
      overlayVisibility = 0;
    }
    return {
      opacity: withTiming(overlayVisibility),
      zIndex: withTiming(overlayVisibility === 0 ? -1 : 1),
    };
  }, [overlayVisibilityValue.value, whileRotateSV?.value]);

  // useEffect(() => {
  //   !isOnline && onBackPress();
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [isOnline]);

  const onBackPress = useCallback(
    (isLastEpisode) => {
      player?.destroyPlayer?.();
      //hide all the CommonModal when network goes offline
      CommonModal?.hide();

      if (appConfig?.appRatingPrompt?.()) {
        requestInAppReview();
      }
      triggerMediaEndEvents();
      if (fromHeroBanner) {
        navigation?.goBack?.();
        //commenting for now until cofirmed.
        //        if (
        //                  [V3_CONTENT_TYPE.EPISODE, V3_CONTENT_TYPE.SEASON].includes(
        //                    props?.contentType
        //                  )
        //                ) {
        //                  const independentTvShow = isIndependentTvShow({
        //                    contentType: props?.contentType,
        //                    seriesId: props?.seriesId,
        //                    seasonId: props?.seasonId,
        //                  });
        //                  navigation?.replace?.(Routenames.DETAILS_PAGE, {
        //                    seasonId: props?.seasonId,
        //                    contentId: independentTvShow ? contentId : props?.seriesId,
        //                    contentType: independentTvShow
        //                      ? V3_CONTENT_TYPE.EPISODE
        //                      : V3_CONTENT_TYPE.SERIES,
        //                  });
        //                } else {
        //                  navigation?.replace?.(Routenames.DETAILS_PAGE, {
        //                    contentId,
        //                    contentType: props?.contentType,
        //                  });
        //                }
      } else {
        if (isLastEpisode) {
          dispatch(
            getLastEpisodePlaybackStatus({
              isLastEpisode: true,
            })
          );
          navigation?.goBack?.();
        } else {
          navigation?.goBack?.();
        }
      }
      //exit the pip when user press back button
      player.exitPictureInPicture();
    },
    [
      player,
      triggerMediaEndEvents,
      fromHeroBanner,
      props?.contentType,
      props?.seasonId,
      props?.seriesId,
      navigation,
      contentId,
      dispatch,
    ]
  );

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const triggerHeartbeatApi = useCallback(
    throttle(
      (time) => {
        if (!isAdPlayingRef.current && parseInt(time, 10) !== 0) {
          callHeartBeatFunction(time, totalDurationRef.current);
        }
      },
      cwPollingInterval,
      { leading: true, trailing: false }
    )
  );

  const seekCastPlayer = useCallback(
    (duration) => {
      castClient && castClient.seek({ position: duration });
    },
    [castClient]
  );

  const genericSeek = useCallback(
    (duration, ifAirplayConnected) => {
      let updateTime = Math.round(duration);
      if (ifAirplayConnected) {
        updateTime = control$.currentTime.get() + duration || 0;
        updateTime = Math.round(updateTime);
      }
      if (googleCastConnected) {
        seekCastPlayer(duration);
      }
      return updateTime > 0 ? updateTime : 0;
    },
    [control$.currentTime, googleCastConnected, seekCastPlayer]
  );

  const seekTo = useCallback(
    (time = 0, ifAirplayConnected = false) => {
      onSeekValueRef.current = time;
      try {
        let updatedTime = genericSeek(time, ifAirplayConnected);
        updateCurrentSkipInfo();
        triggerHeartbeatApi.cancel();
        PlayerEventRefs.current?.updateSeekStatus?.();
        let totalDuration = totalDurationRef.current;
        if (updatedTime <= Math.round(totalDuration)) {
          player?.seek(updatedTime);
          control$?.currentTime?.set?.(updatedTime);
        } else {
          if (updatedTime > Math.round(totalDuration)) {
            applyCustomToast(
              dictionary?.unableToFastForward ??
                translate('unableToFastForward'),
              '',
              ToastType.SOOKA_TOAST
            );
          }
        }
      } catch (err) {
        console.log('Player Seek', err);
      }
    },
    [
      player,
      translate,
      genericSeek,
      triggerHeartbeatApi,
      control$?.currentTime,
      updateCurrentSkipInfo,
      dictionary?.unableToFastForward,
    ]
  );

  const addPlayerEvent = (eventData, key) => {
    if (typeof PlayerEventRefs.current[key] === 'function') {
      PlayerEventRefs.current?.[key]?.(eventData);
    }
    if (key === 'onLoad' && !onLoadFlag) {
      setOnLoadFlag(true);
    }
  };

  function onLoad(eventData) {
    // video playback started
    setFirstFrameLoaded(true);

    playbackRetryCount.current = 0;
    control$?.isReloadingPlayer?.set(false);
    const fbEventParams = {
      title: metaData?.title,
      contentId: contentId,
    };
    runOnJS(setOverlayTimeoutShow)();
    videoPlayEventFB(fbEventParams);
    if (!adTagUrl) {
      initialLoader.value = 0;
      control$?.isMuted?.set?.(false);
    }
    totalDurationRef.current = eventData?.duration;
    control$?.duration?.set?.(eventData?.duration);
    addPlayerEvent(eventData, 'onLoad');
    setOverlayTimeoutHide();
    control$?.isBuffering?.set?.(false);
    control$?.playerState?.set?.(PLAYER_STATE.READY);
    if (utils.isAndroid) {
      const updatedResolutions = videoResolutionsRef.current?.length
        ? [...videoResolutionsRef.current, ...(eventData?.videoTracks || [])]
        : eventData?.videoTracks || [];
      const uniqueResolutions = removeDuplicateResolutions(updatedResolutions);
      videoResolutionsRef.current =
        uniqueResolutions && Object.keys(uniqueResolutions)?.length
          ? sortResolution(Object.values(uniqueResolutions))
          : [];
      performVideoResolutionOperation(false);
    }

    if (utils.isIOS) {
      lastSelectedVideoTrackRef.current = null;
    }
  }

  function onLoadStart(eventData) {
    control$?.isBuffering?.set?.(true);
  }

  function onEnd() {
    const fbEventParams = {
      title: metaData?.title,
      contentId: contentId,
      duration: totalDurationRef.current,
    };
    videoPlayEndEventFB(fbEventParams);
    /**
     * Commented below code because there is no need of bingeWatch in pip as of now.
     */
    // if (metaData.contentType === CONTENT_TYPE.MOVIES || utils.isIOS) {
    player.exitPictureInPicture();
    // }

    playerVolume.value = 0;
    addPlayerEvent(null, 'onEnd');
    if (
      metaData.contentType === V3_CONTENT_TYPE.EPISODE &&
      !metaData.isLastEpisode
    ) {
      playNextEpisode();
    } else {
      if (playTrailer && accessToken) {
        overlayVisibilityValue.value = 0;
        toggleTrailerToContent(false);
        setNextEpisodeLoader(true);
        setTimeout(() => {
          setOverlayTimeoutShow();
          requestAnimationFrame(() => {
            setNextEpisodeLoader(false);
          });
        }, 500);
      } else {
        if (isDefaultPlayerModeLandscape) {
          onBackPress(metaData?.isLastEpisode);
          player.exitPictureInPicture();
        } else {
          control$?.isPaused?.set?.(true);
          control$?.playerState?.set?.(PLAYER_STATE.STOP);
          control$?.fullScreen?.set?.(false);
          changeOrientation();
          clearOverlayTimer();
          overlayVisibilityValue.value = 1;
        }
      }
    }
    PlayerEventRefs.current = {};
  }

  async function playNextEpisode() {
    if (
      metaData?.isLastEpisode ||
      !metaData?.nextEpisodeMetaData ||
      (metaData?.nextEpisodeMetaData &&
        !Object.keys(metaData?.nextEpisodeMetaData)?.length)
    ) {
      /* Check if next Episode is available or not */
      return;
    }
    preRollAdsPlayed.current = false;
    control$?.nextEpisodeTriggered?.toggle?.();
    triggerMediaEndEvents();

    const {
      id = '',
      title = '',
      entitlements = [],
      isContentEntitled = false,
    } = getNextContentInfo();

    if (true) {
      //currently setting it to true for the binge watch flow
      player?.destroyPlayer?.();
      videoQuartileRef.current = {};
      control$.selectedVideoTrack?.set?.({ bitrate: props?.initialABR });
      lastSelectedVideoTrackRef.current = selectedVideoTrack;
      setOnLoadFlag(false);
      PlayerEventRefs.current?.onEnd?.();
      updateCurrentSkipInfo();
      clearOverlayTimer();
      initialLoader.value = 1;
      overlayVisibilityValue.value = 0;
      setNextEpisodeLoader(true);
      if (playTrailer) {
        toggleTrailerToContent(false);
        requestAnimationFrame(() => {
          setNextEpisodeLoader(false);
        });
        return;
      } else {
        triggerHeartbeatApi.cancel();
        callHeartBeatFunction(currentTimeRef.current, totalDurationRef.current);
        playNextContent(() => {
          requestAnimationFrame(() => {
            setNextEpisodeLoader(false);
          });
        });
      }
      //don't pause for next episode
      control$?.isPaused?.set?.(false);
      registerAnalyticsEvent(GA_EVENT_NAMES.PLAYER_SETTINGS, {
        [GA_KEYS.ITEM_LIST]: metaData?.nextEpisodeMetaData?.title || title,
        [GA_KEYS.ITEM_CATEGORY]: GA_VALUES.BINGE_WATCH,
        [GA_KEYS.ITEM_NAME]: GA_VALUES.NEXT_EPISODE,
      });
      registerAnalyticsEvent(GA_EVENT_NAMES.PLAYER_CHANGE_MEDIA_EVENT, {
        [GA_KEYS.EVENT_CATEGORY]: CONVIVA_PLAYER_NAME,
        [GA_KEYS.EVENT_LABEL]: metaData?.nextEpisodeMetaData?.title || title,
        [GA_KEYS.VIDEO_TITLE]: metaData?.nextEpisodeMetaData?.title || title,
        [GA_KEYS.VIDEO_ID]: metaData?.nextEpisodeMetaData?.contentId || id,
        [GA_KEYS.EVENT_ACTION]: GA_VALUES.PLAYER_CHANGE_MEDIA_EVENT_ACTION,
      });
    } else {
      control$?.isPaused?.set?.(true);
      CommonModal?.show(
        showModalObject(
          fullScreen,
          dictionary,
          translate,
          control$,
          navigation,
          title,
          entitlements,
          metaData
        )
      );
    }
    await CacheManager.clearCache();
  }

  function onSeek(eventData) {
    addPlayerEvent(eventData, 'onSeek');
  }

  function onPlaybackRateChange({ playbackRate }) {
    playbackRateRef.current = playbackRate;
  }

  // Timer for buffering duration
  useEffect(() => {
    if (isBuffering && enableSlowNetworkFeature && !error) {
      // Start or continue tracking buffering time
      if (bufferingStartTimeRef.current === 0) {
        bufferingStartTimeRef.current = Date.now();
      }
      const intervalId = setInterval(() => {
        const elapsedTime = (Date.now() - bufferingStartTimeRef.current) / 1000; // seconds
        // to display slow network toast when network drops from good to poor
        if (elapsedTime > slowNetworkToastThreshold && firstFrameLoaded) {
          setShowSlowNetworkToast(true);
        }

        if (elapsedTime > networkThresholdTimeout) {
          console.log('Buffering for more than 30 seconds!');
          control$?.playerState?.set?.(PLAYER_STATE?.STOP);
          control$?.playerState?.set?.(PLAYER_STATE?.PAUSED);
          control$?.isPaused?.set?.(true);
          reportPlaybackError(
            translate('slowNetworkDetectedConvivaDescription')
          );
          player?.destroyPlayer();
          setNextEpisodeLoader(true);
          CommonModal?.show({
            fullScreen,
            title:
              dictionary?.slowNetworkDetected ??
              translate('slowNetworkDetected'),
            description:
              dictionary?.slowNetworkDetectedDescription ??
              translate('slowNetworkDetectedDescription'),
            confirmButtonText: dictionary?.close ?? translate('close'),
            onPressConfirm: () => {
              CommonModal?.hide();
              navigation.goBack();
            },
            onPressClose: () => {
              CommonModal?.hide();
              navigation.goBack();
            },
          });
          setShowSlowNetworkToast(false);
          clearInterval(intervalId);
        }
      }, 1000); // Check every second

      return () => clearInterval(intervalId);
      // Clean up interval on unmount or when buffering stops
    } else {
      // Reset when not buffering
      bufferingStartTimeRef.current = 0;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [control$?.playerState, fullScreen, isBuffering]); // Dependency on isBuffering to track when it changes

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const onBuffer = useCallback(
    debounce(
      (eventData) => {
        control$?.isBuffering?.set?.(eventData.isBuffering);
      },
      500,
      { leading: true, trailing: false } //onBuffer getting called after player gives error
    ),
    [overlayVisibilityValue.value]
  );

  const checkForLoader = throttle(
    useCallback(() => {
      if (isBuffering) {
        control$?.isBuffering?.set?.(false);
      }
    }, [control$?.isBuffering, isBuffering]),
    1000
  );

  function onContentBuffer(eventData) {
    checkForLoader();
    /*
        if (
          eventData.playableDuration !== 0 &&
          eventData.playableDuration <= eventData.currentTime &&
          eventData.currentTime !== eventData.seekableDuration
        ) {
          control$?.isBuffering?.set?.(true);
        } else {
          control$?.isBuffering?.set?.(false);
        }
      */
  }

  function isContentAboutToEnd(eventData) {
    if (
      eventData &&
      eventData.currentTime != null &&
      eventData.seekableDuration != null
    ) {
      const durationThreshold =
        eventData.currentTime / eventData.seekableDuration;

      if (durationThreshold >= cwEndThreshold) {
        if (
          metaData?.isLastEpisode ||
          (!playTrailer && metaData.contentType === V3_CONTENT_TYPE.MOVIE)
        ) {
          /**
           * This is to remove the last episode of the last season from the Watch History.
           * (Remove this once its fixed from backend)
           */
          removeLastContentFromWatchHistory();
        }
      }
    }
  }

  const checkSkipInfoMarker = useCallback(
    (currentTime, totalDuration) => {
      const { skipRecap = {}, skipIntro = {}, skipCredit = {} } = skipInfo;

      if (isAdPlayingRef.current) {
        return;
      }

      if (!currentSkipInfo?.current?.skipFor) {
        if (
          skipRecap?.startTime <= currentTime &&
          skipRecap?.endTime > currentTime
        ) {
          updateCurrentSkipInfo(
            true,
            skipRecap?.endTime,
            skipRecap?.startTime,
            PLAYER_SKIP_MODE.RECAP
          );
          return;
        }
        if (
          (skipIntro.startTime === undefined ||
            skipIntro?.startTime <= currentTime) &&
          skipIntro?.endTime > currentTime
        ) {
          updateCurrentSkipInfo(
            true,
            skipIntro?.endTime,
            skipIntro?.startTime ?? 0,
            PLAYER_SKIP_MODE.INTRO
          );
          return;
        }

        // Check if skipCredit is applicable
        if (
          skipCredit?.startTime <= currentTime &&
          skipCredit?.endTime >= currentTime &&
          !metaData?.isLastEpisode
          //          && !isWatchCreditClicked.current // TODO check if required
        ) {
          if (metaData?.contentType !== V3_CONTENT_TYPE.EPISODE) {
            // TODO: Remove this check once Recommendation Flow is added to the Player
            return;
          }

          if (onSeekValueRef.current < currentTime) {
            return;
          }

          updateCurrentSkipInfo(
            true,
            skipCredit?.endTime,
            skipCredit?.startTime,
            PLAYER_SKIP_MODE.CREDIT
          );

          /**
           * 2. After 10 sec - Reset currentSkipInfo and call play next content method.
           * 3. Next Episode Click - Reset currentSkipInfo and call play next content method.
           */
          return;
        } else {
          if (
            !skipCredit?.endTime
              ? currentTime >=
                totalDuration - (totalDuration > thirtyMinutes ? 30 : 10)
              : currentTime >= totalDuration - 10
          ) {
            //when the total duration will be more than 30 minutes the pop would appear before 30 seconds, else 10 seconds before the content ends.(when there will be no skipCredit)
            updateCurrentSkipInfo(
              true,
              skipCredit?.endTime,
              skipCredit?.startTime,
              PLAYER_SKIP_MODE.CREDIT
            );
          }
        }
      } else if (
        currentTime >= currentSkipInfo?.current?.endTime ||
        currentTime <= currentSkipInfo?.current?.startTime
      ) {
        // Skip credit will be shown 10 sec before the content ends if the user clicked 'watch credit' before
        if (currentTime <= totalDuration - 10 && skipCredit?.endTime) {
          updateCurrentSkipInfo();
        } else {
          return;
        }
      }
    },
    [
      metaData?.contentType,
      skipInfo,
      updateCurrentSkipInfo,
      metaData?.isLastEpisode,
    ]
  );

  const updateCurrentSkipInfo = useCallback(
    (
      skipFor = false,
      endTime = undefined,
      startTime = undefined,
      mode = PLAYER_SKIP_MODE?.NONE
    ) => {
      // no need to show overlay control when skip credit is visible so commented the code
      // if (skipFor) {
      //   setOverlayTimeoutShow();
      // }
      if (mode === PLAYER_SKIP_MODE?.CREDIT && metaData.isLastEpisode) {
        currentSkipInfo.current.skipFor = false;
        currentSkipInfo.current.endTime = undefined;
        currentSkipInfo.current.startTime = undefined;
        control$?.skipInfoMode?.set?.(PLAYER_SKIP_MODE?.NONE);
        return;
      }
      currentSkipInfo.current.skipFor = skipFor;
      currentSkipInfo.current.endTime = endTime;
      currentSkipInfo.current.startTime = startTime;
      control$?.skipInfoMode?.set?.(mode);
    },
    [control$?.skipInfoMode, metaData.isLastEpisode]
  );

  const checkTokenExpiry = useCallback(() => {
    const currentTime = Math.floor(Date.now() / 1000); // Get current time in seconds
    // Calculate time until token expiry
    const timeUntilExpiry = wmTokenExpTime.current - currentTime;
    // If less than 2 minutes remaining, call fetchWaterMarkToken
    if (timeUntilExpiry <= 120) {
      fetchWaterMarkToken()
        .then((result) => {
          setWmToken(result?.token);
          wmTokenExpTime.current = result?.expiresIn;
        })
        .catch(onPlayerError);
    }
  }, [fetchWaterMarkToken, onPlayerError]);

  const handleWaterMarkRefresh = useCallback(() => {
    if (metaData?.contentType === V3_CONTENT_TYPE.TRAILER || utils?.isIOS) {
      return; // WE DON'T NEED THIS LOGIC FOR TRAILER AND IOS
    }
    removeWmTokenTimeOut();
    const currentTime = Math.floor(Date.now() / 1000); // Get current time in seconds
    // Calculate time until token expiry
    const timeUntilExpiry = wmTokenExpTime.current - currentTime;
    timeoutId.current = setTimeout(
      checkTokenExpiry,
      timeUntilExpiry - WATERMARK_REFRESH_TOKEN_TIME
    );
    // Call checkTokenExpiry() to check the token expiry
    checkTokenExpiry();
  }, [checkTokenExpiry, metaData?.contentType]);

  function onProgress(eventData) {
    if (!eventData) {
      console.error('onProgress: eventData is null or undefined');
      return;
    }
    const { currentTime } = eventData;
    if (isNaN(currentTime)) {
      console.error('onProgress: currentTime is NaN');
      return;
    }
    onSeekValueRef.current = currentTime;
    if (utils.isIOS && adBreakStartedRef.current && !isAdPlayingRef.current) {
      adBreakStartedRef.current = false;
      reportAdEnded();
      reportAdBreakEnded();
      resumeContentFromAds();
    }

    const percentWatched = Math.ceil(
      (currentTime / totalDurationRef.current) * 100
    );
    const fbEventParams = {
      contentId: contentId,
      duration: currentTime,
      title: metaData?.title,
    };
    if (percentWatched >= 25 && !videoQuartileRef?.current?.first) {
      videoFirstQuartilePlayEventFB(fbEventParams);
      videoQuartileRef.current.first = true;
      registerGaVideoWatchedPercentage(
        25,
        metaData?.title,
        contentId,
        totalDurationRef.current
      );
    } else if (percentWatched >= 50 && !videoQuartileRef?.current?.second) {
      videoSecondQuartilePlayEventFB(fbEventParams);
      videoQuartileRef.current.second = true;
      registerGaVideoWatchedPercentage(
        50,
        metaData?.title,
        contentId,
        totalDurationRef.current
      );
    } else if (percentWatched >= 75 && !videoQuartileRef?.current?.third) {
      videoThirdQuartilePlayEventFB(fbEventParams);
      videoQuartileRef.current.third = true;
      registerGaVideoWatchedPercentage(
        75,
        metaData?.title,
        contentId,
        totalDurationRef.current
      );
    } else if (percentWatched >= 99 && !videoQuartileRef?.current?.end) {
      videoQuartileRef.current.end = true;
      registerGaVideoWatchedPercentage(
        99,
        metaData?.title,
        contentId,
        totalDurationRef.current
      );
    }

    onContentBuffer(eventData);
    if (!playTrailer && !isAdPlayingRef.current) {
      if (isWatchCreditClicked.current) {
        setTimeout(() => {
          isWatchCreditClicked.current = false;
        }, 1000);
      }
      isContentAboutToEnd(eventData);
      checkSkipInfoMarker(eventData.currentTime, eventData?.seekableDuration);
    }
    addPlayerEvent(eventData, 'onProgress');
    currentTimeRef.current = eventData?.currentTime ?? 0;
    if (accessToken && !isAdPlayingRef.current) {
      triggerHeartbeatApi(eventData?.currentTime);
    }
    handleWaterMarkRefresh();

    if (isBuffering) {
      control$?.isBuffering?.set?.(false);
    }
    if (showSlowNetworkToast) {
      setShowSlowNetworkToast(false);
    }
  }

  const reloadVideo = () => {
    control$?.isReloadingPlayer?.set(true);
    // Increment the key to trigger a re-render and reload the video
    setVideoPlayerKey((prevKey) => prevKey + 1);
  };

  async function onError(errorEvent) {
    console.log('onError', errorEvent);
    // Throwing this error from RNVideo when required data is missing.
    if (errorEvent === PLAYER_GENERIC_ERROR_CODE.UNKNOWN) {
      checkForUnknownError(errorEvent);
      return;
    }
    if (disablePlayerReloading.current) {
      return;
    }
    const { errorCode, errorMessage } = await extractErrorInfo(
      errorEvent?.error,
      metaData,
      wmToken
    );

    const { code: failureResponseCode } =
      (errorEvent?.error?.failureResponse &&
        JSON.parse(errorEvent?.error?.failureResponse)) ||
      {};

    const isBusinessError = (businessErrorCode) => {
      if (
        businessErrorCode &&
        (metaDataRef.current?.isContentExpired ||
          [
            DRM_ERROR_CODES.LICENSE_DENIED_NO_ENTITLEMENT,
            DRM_ERROR_CODES.CONTENT_NOT_AVAILABLE_AT_LOCATION,
            DRM_ERROR_CODES.LICENSE_DENIED_MAX_STREAMS,
            DRM_ERROR_CODES.TOO_MANY_CONCURRENT_STREAMS,
            DRM_ERROR_CODES.TOO_MANY_CONCURRENT_STREAMS_FOR_DEVICE_TYPE,
          ].includes(businessErrorCode))
      ) {
        return true;
      }
      return false;
    };

    if (utils.isIOS && isBusinessError(failureResponseCode)) {
      //In this case, do not report error from here! useEffect will handle this flow.
      disablePlayerReloading.current = true;
    }

    if (
      utils?.isIOS &&
      playbackRetryCount.current <= maxRetryCount &&
      playerReloadErrorType?.[errorCode]
    ) {
      if (!disablePlayerReloading.current) {
        playbackRetryCount.current = playbackRetryCount.current + 1;
        reloadVideo();
      }
      disablePlayerReloading.current = false;
      return;
    } else if (
      isReloadingPlayer &&
      playbackRetryCount.current > maxRetryCount
    ) {
      control$?.isReloadingPlayer?.set(false);
    }

    if (errorEvent?.error?.failureResponse) {
      let failureResponse = JSON.parse(errorEvent?.error?.failureResponse);
      onPlayerError(failureResponse?.code);
    }

    const convivaErrorMessage =
      errorCode || errorMessage
        ? `${errorCode} ${errorMessage}`
        : (dictionary?.generic_player_error_description ??
          translate('generic_player_error_description'));

    // Report Error on Sentry
    logPlayerErrorToSentry({
      errorMessage,
      message: errorCode,
      details: {
        convivaErrorMessage,
        metaData: { ...metaData, adTagUrl },
        errorStack: JSON.stringify(errorEvent),
      },
    });

    if (errorEvent?.error?.failureResponse) {
      return;
    }

    onPlayerError(errorCode);

    if (convivaErrorMessage) {
      if (isBusinessError(errorCode)) {
        //In this case, do not report error from here! useEffect will handle this flow.
        return false;
      }
      /**
       * Hot Fix:
       * Delaying error reporting if ads are playing before the player throws an error.
       * If the player error is reported before the Conviva `ad.podEnd` event,
       * Conviva will not log the playback error on the dashboard.
       * */
      if (utils?.isAndroid && isAdPlayingRef.current) {
        setTimeout(() => {
          reportPlaybackError(errorMessage);
        }, 750);
      } else {
        reportPlaybackError(errorMessage);
      }
    }
  }

  function checkForUnknownError(errorCode) {
    if (
      metaDataRef.current?.playbackUrl &&
      ((metaDataRef.current?.isWMEnabled === true &&
        !metaDataRef.current?.wmToken &&
        metaDataRef.current?.isDrmEnabled === true &&
        !metaDataRef.current?.drmToken) ||
        (metaDataRef.current?.isWMEnabled === true &&
          !metaDataRef.current?.wmToken &&
          metaDataRef.current?.isDrmEnabled === false) ||
        (metaDataRef.current?.isWMEnabled === false &&
          metaDataRef.current?.isDrmEnabled === true &&
          !metaDataRef.current?.drmToken))
    ) {
      onPlayerError(errorCode);
    }
  }

  // eslint-disable-next-line react-hooks/exhaustive-deps
  function onPlayerError(errorCode = '') {
    clearOverlayTimer();
    removeWmTokenTimeOut();
    overlayVisibilityValue.value = 1;
    control$?.isPaused?.set?.(true);
    control$?.isBuffering?.set?.(false);
    control$?.playerState?.set?.(PLAYER_STATE?.STOP);
    // control$?.selectedModal?.set?.(PLAYER_MODAL_TYPES.ERROR);
    if (!error) {
      control$?.error?.set?.(errorCode || true);
    }
  }

  const onScreenTap = () => {
    if (playerState === PLAYER_STATE.STOP) {
      return;
    }
    if (overlayVisibilityValue.value === 0) {
      setOverlayTimeoutShow();
    } else {
      overlayVisibilityValue.value = 0;
    }
  };

  /* clearOverlayTimer */
  const clearOverlayTimer = () => {
    if (hideControlsTimeOutId.current) {
      clearTimeout(hideControlsTimeOutId.current);
      hideControlsTimeOutId.current = null;
    }
  };

  const setOverlayTimeoutHide = useCallback(() => {
    clearOverlayTimer();
    hideControlsTimeOutId.current = setTimeout(() => {
      overlayVisibilityValue.value = 0;
    }, DEFAULT_HIDE_OVERLAY_TIME);
  }, [hideControlsTimeOutId, overlayVisibilityValue]);

  const setOverlayTimeoutShow = useCallback(() => {
    if (selectedModal !== PLAYER_MODAL_TYPES.NONE) {
      if (error) {
        return;
      }
    }
    overlayVisibilityValue.value = 1;
    if (controlsHide) {
      setOverlayTimeoutHide();
    }
  }, [
    overlayVisibilityValue,
    setOverlayTimeoutHide,
    selectedModal,
    error,
    control$?.selectedModa?.get(),
  ]);

  const setOverlayHideWithoutTimeout = useCallback(() => {
    clearOverlayTimer();
    overlayVisibilityValue.value = 0;
  }, [overlayVisibilityValue]);

  const initialLoaderStyle = useAnimatedStyle(() => {
    return {
      opacity: withTiming(initialLoader.value, { duration: 750 }),
      zIndex: withTiming(
        initialLoader.value ? 5 : -5,
        { duration: 750 },
        (finished) => {
          if (finished && !error) {
            playerVolume.value = 1;
          }
        }
      ),
    };
  });

  const playerStyle = useAnimatedStyle(() => {
    const padding = hideAllOverlay.value
      ? utils?.isTab && utils?.isIOS
        ? safeEdges?.top || SAFE_EDGES.top
        : safeEdges?.left || SAFE_EDGES.left
      : 0;

    return utils?.isTab
      ? {
          paddingVertical: withTiming(padding, { duration: 150 }),
        }
      : {
          paddingHorizontal: withTiming(padding, { duration: 150 }),
        };
  }, [hideAllOverlay.value]);

  const onAudioFocusChanged = (audioFocus) => {
    if (audioFocus?.hasAudioFocus) {
      control$.isPaused.set(false);
    } else {
      control$.isPaused.set(true);
    }
  };

  const onPictureInPictureStatusChanged = ({ isActive }) => {
    if (isActive) {
      control$.selectedModal.set(PLAYER_MODAL_TYPES.NONE);
    }
  };

  const onPlaybackStateChanged = useCallback(
    (event) => {
      if (appState === APP_STATE.BACKGROUND) {
        control$.isPaused.set(!event?.isPlaying);
      } else {
        // Safe bet to stop player orientation
        navigation?.setOptions({
          orientation: 'landscape',
        });
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [appState]
  );

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const resumeContentFromAds = useCallback(
    throttle(
      () => {
        initialLoader.value = 0;
        control$.resizeMode?.set?.(prevResizeMode.current);
        if (utils?.isIOS) {
          if (isMuted) {
            control$?.isMuted?.set?.(false);
          }
          control$?.isPaused?.set?.(false);
        }
        isAdPlayingRef.current = false;
        hideAllOverlay.value = false;
        setOverlayTimeoutShow();
        preRollAdsPlayed.current = true;

        //commented this code, no need to perform resolution capping after ads ends.
        // performVideoResolutionOperation(false, false);
      },
      500,
      { leading: false, trailing: true }
    ),
    []
  );

  /**
   *
   * @param {*} clearRefData
   * @param {*} skipLastResolution
   * this method is use for getting video reolutions and setting reolution on player
   */

  const performVideoResolutionOperation = (
    clearRefData = false,
    skipLastResolution = false
  ) => {
    // Set up resolution data
    if (videoResolutionsRef.current?.length) {
      videoResolutionsRef.current = utils?.isIOS
        ? sortResolutionBandwidth(videoResolutionsRef.current)
        : sortResolutionByHeight(videoResolutionsRef.current);

      const { filteredResolutions, selectedVideoTrack: _selectedVideoTrack } =
        getValidResolutions(
          videoResolutionsRef.current,
          metaDataRef.current?.cappedResolution,
          metaDataRef.current?.contentMaxResolution,
          metaDataRef.current?.platformResolutionConfig
        );
      /**
       * adding only 3 resolion in the list if list.length > 3.
       * After discussion, we are not slicing list of filteredResolutions
       */
      // if (filteredResolutions?.length > 3) {
      //   control$.videoTracks?.set?.(
      //     filteredResolutions.slice(filteredResolutions.length - 3)
      //   );
      //   // control$.videoTracks?.set?.(filteredResolutions);
      // } else {
      control$.videoTracks?.set?.(filteredResolutions);
      // }
      if (lastSelectedVideoTrackRef.current?.height && !skipLastResolution) {
        const resolution = filteredResolutions?.find(
          (item) => item?.height === lastSelectedVideoTrackRef.current?.height
        );
        if (
          resolution &&
          !resolution?.platformNotSupported &&
          !resolution?.addResolutionCapping
        ) {
          control$.selectedVideoTrack?.set?.(lastSelectedVideoTrackRef.current);
        } else {
          control$.selectedVideoTrack?.set?.(_selectedVideoTrack);
        }
      } else {
        control$.selectedVideoTrack?.set?.(_selectedVideoTrack);
      }
      if (utils.isIOS || clearRefData) {
        if (utils.isIOS) {
          videoResolutionsRef.current = null;
        }
        if (utils.isAndroid) {
          lastSelectedVideoTrackRef.current = null;
        }
      }
    }
  };

  const onReadyForDisplay = () => {
    registerAnalyticsEvent(GA_EVENT_NAMES.PLAYER_PLAY_EVENT, {
      [GA_KEYS.EVENT_CATEGORY]: CONVIVA_PLAYER_NAME,
      [GA_KEYS.EVENT_LABEL]: metaDataRef.current?.title,
      [GA_KEYS.VIDEO_TITLE]: metaDataRef.current?.title,
      [GA_KEYS.VIDEO_ID]: metaDataRef.current?.contentId,
      [GA_KEYS.EVENT_ACTION]: GA_VALUES.PLAYER_PLAY_EVENT_ACTION,
    });
    registerAnalyticsEvent(GA_EVENT_NAMES.PLAYER_OPEN_FULL_SCREEN_EVENT, {
      [GA_KEYS.EVENT_CATEGORY]: CONVIVA_PLAYER_NAME,
      [GA_KEYS.EVENT_LABEL]: metaDataRef.current?.title,
      [GA_KEYS.VIDEO_TITLE]: metaDataRef.current?.title,
      [GA_KEYS.VIDEO_ID]: metaDataRef.current?.contentId,
      [GA_KEYS.EVENT_ACTION]: GA_VALUES.PLAYER_OPEN_FULL_SCREEN_EVENT_ACTION,
    });
  };

  /**
   * Handles the reception of ad events and updates the player state accordingly.
   *
   * @param {Object} event - The ad event object containing the event type and associated data.
   * @param {string} event.event - The type of ad event received (e.g., LOADED, STARTED, COMPLETED).
   * @param {Object} [event.data={}] - Additional data related to the ad event, such as error type.
   */
  const onReceiveAdEvent = (event) => {
    const { event: adEvent, data = {}, adInfo = {} } = event || {};
    const errorCode = extractAdsErrorNumber(data?.code);

    // If no ad event is received, exit the function early.
    if (!adEvent) {
      return;
    }

    // Handle ad errors differently for Android and iOS platforms due to structural differences.
    const errorType = data?.type || data?.logData?.type;
    if (
      [AD_ERROR_TYPE.LOAD_ERROR, AD_ERROR_TYPE.PLAY_ERROR].includes(
        errorType
      ) ||
      ADS_ERROR_CODES?.[Platform.OS]?.[errorCode]
    ) {
      logPlayerAdsErrorToSentry({
        details: {
          metaData: { ...metaData, adTagUrl },
          errorStack: JSON.stringify({
            event,
            info: errorType ?? ADS_ERROR_CODES?.[Platform.OS]?.[errorCode],
          }),
        },
        message: errorType ?? ADS_ERROR_CODES?.[Platform.OS]?.[errorCode]?.name,
      });
      reportAdError(errorType ?? ADS_ERROR_CODES?.[errorCode]?.name);
      control$?.isMuted?.set?.(false);
      hideAllOverlay.value = false;
    }

    // Handle different types of ad events based on the event type.
    switch (adEvent) {
      case AD_EVENTS.LOADED:
        setFirstFrameLoaded(false);
        resumeContentFromAds.cancel();
        break;
      case AD_EVENTS.FIRST_QUARTILE:
      case AD_EVENTS.MIDPOINT:
      case AD_EVENTS.THIRD_QUARTILE:
        reportAdPlayPausedState(false);
        break;
      case AD_EVENTS.CLICK:
      case AD_EVENTS.CLICKED:
        if (utils.isIOS) {
          adClickRef.current = true;
          reportAppBackgrounded();
        }
        break;
      case AD_EVENTS.PAUSED:
        reportAdPlayPausedState(true);
        break;
      case AD_EVENTS.STARTED:
        const _adInfo = getConvivaAdInfo({
          isLive: false,
          adTagUrl: adTagUrl,
          streamUrl: metaData?.playbackUrl,
          viewerId:
            (userData?.tokenDetails?.accessToken &&
              userData?.subscriber?.customerId) ||
            (dictionary?.guest_user ?? translate('guest_user')),
          adType: dictionary?.client_side ?? translate('client_side'),
          ...adInfo,
        });
        reportAdStarted(_adInfo);
        reportAdPlayPausedState(false);
        if (adInfo?.VASTMediaBitrate) {
          reportAdBitrate(adInfo.VASTMediaBitrate);
        }
        if (!adBreakStartedRef.current) {
          adBreakStartedRef.current = true;
          reportAdBreakStarted(true, false);
        }
        control$.resizeMode?.set?.(PLAYER_RESIZE_MODE.CONTAIN);
        initialLoader.value = 0;
        if (utils?.isAndroid) {
          control$?.isMuted?.set?.(false);
          control$?.isPaused?.set?.(false);
        }
        hideAllOverlay.value = true;
        isAdPlayingRef.current = true;
        updateCurrentSkipInfo();
        control$.selectedModal.set(PLAYER_MODAL_TYPES.NONE);
        break;
      case AD_EVENTS.RESUMED:
        reportAdPlayPausedState(false);
        control$?.isPaused?.set?.(false);
        if (utils.isIOS && adClickRef.current) {
          reportAppForegrounded();
        }
        break;
      case AD_EVENTS.CONTENT_RESUME_REQUESTED:
        control$?.isPaused?.set?.(false);
        hideAllOverlay.value = false;
        isAdPlayingRef.current = false;
        if (utils.isAndroid && !onLoadFlag) {
          control$?.isPaused?.set?.(true);
          requestAnimationFrame(() => {
            control$?.isPaused?.set?.(false);
          });
        }
        if (utils.isAndroid && adBreakStartedRef.current) {
          adBreakStartedRef.current = false;
          reportAdEnded();
          reportAdBreakEnded();
          resumeContentFromAds();
        }
        break;
      case AD_EVENTS.SKIPPED:
        setFirstFrameLoaded(true);
        reportAdSkipped();
        reportAdEnded();
        if (utils?.isIOS && adBreakStartedRef.current) {
          adBreakStartedRef.current = false;
          resumeContentFromAds();
        }
        break;
      case AD_EVENTS.ALL_ADS_COMPLETED:
        setFirstFrameLoaded(true);
        if (adBreakStartedRef.current) {
          adBreakStartedRef.current = false;
          reportAdEnded();
          reportAdBreakEnded();
          resumeContentFromAds();
        }
        break;
      case AD_EVENTS.COMPLETED:
        reportAdEnded();
        if (utils?.isIOS && adBreakStartedRef.current) {
          adBreakStartedRef.current = false;
          reportAdEnded();
          reportAdBreakEnded();
          resumeContentFromAds();
        }
        break;
      case AD_EVENTS.ERROR:
      case AD_EVENTS.UNKNOWN:
      case AD_EVENTS.AD_BREAK_FETCH_ERROR:
        adBreakStartedRef.current = false;
        reportAdEnded();
        reportAdBreakEnded();
        resumeContentFromAds();
        break;
      default:
        break;
    }
  };

  function removeWmTokenTimeOut() {
    if (timeoutId?.current) {
      clearTimeout(timeoutId.current);
    }
  }

  let gestureStarted = false;
  let previousScale = 1;

  const handleGesture = (evt) => {
    const { state, scale } = evt.nativeEvent;
    switch (state) {
      case State.BEGAN:
        gestureStarted = true;
        previousScale = scale;
        break;

      case State.ACTIVE:
        if (gestureStarted) {
          if (scale > previousScale + SCALECONSTANT.addSacle) {
            const currentMode = control$?.resizeMode?.get();
            const newMode =
              currentMode === PLAYER_RESIZE_MODE.CONTAIN
                ? PLAYER_RESIZE_MODE.COVER
                : currentMode;
            control$?.resizeMode?.set(newMode);
          } else if (scale < previousScale + SCALECONSTANT.addSacle) {
            const currentMode = control$?.resizeMode?.get();
            const newMode =
              currentMode === PLAYER_RESIZE_MODE.COVER
                ? PLAYER_RESIZE_MODE.CONTAIN
                : currentMode;
            control$?.resizeMode?.set(newMode);
          }

          previousScale = scale;
        }
        break;
      case State.END:
        gestureStarted = false;
        break;

      case State.FAILED:
        // Reset in case of failure
        gestureStarted = false;
        break;
    }
  };

  return (
    <Animated.View style={[styles.videoPlayerLandscape, playerStyle]}>
      <GestureHandlerRootView style={styles.gestureViewStyle}>
        <PinchGestureHandler
          onGestureEvent={handleGesture}
          onHandlerStateChange={handleGesture}
        >
          <View>
            {!unmountPlayer &&
              player.getPlayerComponent({
                onEnd,
                onSeek,
                onLoad,
                onError,
                onBuffer,
                onProgress,
                onLoadStart,
                bufferConfig,
                repeat: false,
                muted: false,
                videoPlayerKey,
                volume: isMuted ? 0 : playerVolume.value,
                controls: false,
                onReceiveAdEvent,
                isReloadingPlayer,
                onReadyForDisplay,
                fullScreen: false,
                onAudioFocusChanged,
                onPlaybackRateChange,
                minLoadRetryCount: 5,
                title: metaData?.title,
                playerReloadErrorType,
                playWhenInactive: true,
                enableConvivaVideoAnalytics,
                ignoreSilentSwitch: 'ignore',
                posterUri: metaData?.posterUrl,
                sourceUri: metaData?.dmData?.localUrl,
                // localUri: metaData?.dmData?.localUrl,
                offline: true,
                drmData: {
                  offlineDrm:
                    Platform.OS == 'ios' ? null : metaData?.dmData?.keySetId,
                  licenseServer: metaData?.dmData?.mLicenseAcquisitionURL,
                  offline: true,
                  certificateUrl: '',
                },
                preferredForwardBufferDuration: preferredForwardBufferDuration,
                playerStyle: styles.videoPlayerStyle,
                ContentPosterUri: metaData?.posterUrl,
                drmCredentials: metaData?.drmCredentials,
                extensionRendererMode: props?.extensionRendererMode,
                initialBitrateEstimate: props?.initialBitrateEstimate,
                adTagUrl: !preRollAdsPlayed?.current ? adTagUrl : null,
                startPosition: isNaN(secondsOfContentWatched)
                  ? 0
                  : secondsOfContentWatched,
                convivaContentInfo: {
                  ...convivaContentInfo,
                  railID: railId,
                },
                /* @legendapp/state */
                resizeMode,
                posterResizeMode,
                transform: [{ scale }],
                ref: videoRef,
                isPaused: isPlayerPauseState,
                selectedAudioTrack: {
                  type: selectedAudioTrack?.type,
                  value: selectedAudioTrack?.index,
                },
                selectedTextTrack: {
                  type: selectedTextTrack?.type,
                  value: selectedTextTrack?.index,
                },
                playInBackground: playInBackground,
                rate: selectedPlaybackSpeed?.value || 1,
                maxBitRate: selectedVideoTrack?.bitrate,
                pictureInPicture: enablePictureInPicture,
                onPlaybackStateChanged: onPlaybackStateChanged,
                showNotificationControls: utils.isAndroid ? false : true,
                distroyConvivaAfterReload:
                  playbackRetryCount.current > maxRetryCount ? true : false,
                onPictureInPictureStatusChanged:
                  onPictureInPictureStatusChanged,
                subtitleStyle: {
                  paddingBottom:
                    resizeMode === PLAYER_RESIZE_MODE.CONTAIN
                      ? 0
                      : normalize(80),
                },
                playerType: VIDEO_TYPE.VOD,
                tokens: {
                  wmToken: wmToken,
                  drmToken: metaData?.drmToken,
                  wmTokenExpTime: wmTokenExpTime.current,
                  drmTokenExpTime: metaData?.drmTokenExpTime,
                  isWMEnabled: metaData?.isWMEnabled,
                  isDrmEnabled: metaData?.isDrmEnabled,
                },
              })}

            <AdsControl
              control$={control$}
              showAds={hideAllOverlay}
              onBackPress={onBackPress}
            />

            <SkipInfoView
              seekTo={seekTo}
              metaData={metaData}
              control$={control$}
              skipInfoButton={true}
              playNextEpisode={playNextEpisode}
              totalDuration={totalDurationRef.current}
              updateDuration={onSeekValueRef.current}
              overlayVisibilityValue={overlayVisibilityValue}
              onWatchCreditPress={() => (isWatchCreditClicked.current = true)}
            />

            {/** don't show network stats till first frame of video is loaded */}
            {firstFrameLoaded && enableSlowNetworkFeature ? (
              <NetworkStats
                fullScreen={fullScreen}
                showOverlay={overlayVisibilityValue}
                showSlowNetworkToast={showSlowNetworkToast}
                PlayerEventRefs={(eventRefs) =>
                  (PlayerEventRefs.current = eventRefs)
                }
              />
            ) : null}
            {/* <Animated.View style={[styles?.initialLoader, initialLoaderStyle]}>
              <InitialLoader
                control$={control$}
                metaData={metaData}
                videoType={videoType}
                onBackPress={onBackPress}
                contentTitle={contentTitle}
                coverImage={props?.coverImage}
              />
            </Animated.View> */}

            {/* Hide When Ad is playing */}
            <AnimatedPressable
              onPress={onScreenTap}
              style={[styles.playerOverlayWrapper, playerOverlayWrapper]}
            >
              <Show if={isBuffering}>
                <Loader />
              </Show>

              <PlayerOverlayOnCast
                showTitle={true}
                posterUrl={metaData.posterUrl}
                airplayConnected={airplayConnected}
                googleCastConnected={googleCastConnected}
              />

              <Animated.View
                style={[overlayVisibilityStyle, styles.controlsViewContainer]}
              >
                <View style={styles.controlsViewNormal}>
                  {/* <LinearGradient
              end={{ x: 0, y: 1 }}
              colors={overlayColors}
              start={{ x: 0, y: 0.02 }}
              style={styles.controlsLinearGradient}
            /> */}
                  {!nextEpisodeLoader && (
                    <PlayerOverlay
                      offline={true}
                      {...props}
                      goBack={goBack}
                      player={player}
                      seekTo={seekTo}
                      metaData={metaData}
                      control$={control$}
                      videoType={videoType}
                      translate={translate}
                      dictionary={dictionary}
                      navigation={navigation}
                      accessToken={accessToken}
                      playTrailer={playTrailer}
                      onBackPress={onBackPress}
                      playNextEpisode={playNextEpisode}
                      changeOrientation={changeOrientation}
                      clearOverlayTimer={clearOverlayTimer}
                      drmCredentials={metaData?.drmCredentials}
                      PlayerEventRefs={(eventRefs) =>
                        (PlayerEventRefs.current = eventRefs)
                      }
                      setOverlayTimeoutShow={setOverlayTimeoutShow}
                      streamPositionFun={(currentTime) =>
                        (currentTimeRef.current = currentTime ?? 0)
                      }
                      setOverlayTimeoutHide={setOverlayHideWithoutTimeout}
                      isDefaultPlayerModeLandscape={
                        isDefaultPlayerModeLandscape
                      }
                      playerFeatureConfig={
                        PLAYER_FEATURE_CONFIG?.[metaData?.contentType]?.[
                          playerMode
                        ]
                      }
                      isAdPlayingRef={isAdPlayingRef}
                      setControlsHide={setControlsHide}
                    />
                  )}
                </View>
              </Animated.View>
            </AnimatedPressable>

            <PlayerModal
              control$={control$}
              metaData={metaData}
              videoType={videoType}
              navigation={navigation}
              onBackPress={onBackPress}
              setOverlayTimeoutShow={setOverlayTimeoutShow}
              setOverlayTimeoutHide={setOverlayTimeoutHide}
              isDefaultPlayerModeLandscape={isDefaultPlayerModeLandscape}
              seriesData={seriesData}
              viewAllData={viewAllData}
              setViewall={setViewall}
              setControlsHide={setControlsHide}
              contentType={props.contentType}
              translate={translate}
              dictionary={dictionary}
            />
          </View>
        </PinchGestureHandler>
      </GestureHandlerRootView>
    </Animated.View>
  );
}

export default memo(OfflineVodPlayer);
