import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { t as translate } from 'i18next';
import WebView from 'react-native-webview';
import { useSelector } from 'react-redux';
import { SafeAreaView } from 'react-native-safe-area-context';
import CONSTANTS from '@video-ready/common/utils/constant';
import { isValidHttpUrl } from 'mobile/src/utils/commonMethods';
import TabHeader from 'mobile/src/components/TabHeader';
import { COLORS, FONTS } from '@video-ready/common/utils';
import { ScreenHeight } from '../../utils';
import { normalizeFont } from '../../styles/Mixins';
import useLogout from '../../hooks/useLogout';

const SSOLogout = () => {
  const { dictionary } = useSelector((state) => state?.miscellaneous);
  const {
    webViewUrl,
    title,
    webviewRef,
    onWebViewLoad,
    onNavigationStateChange,
    onPressBack,
  } = useLogout();

  return (
    <SafeAreaView style={styles.container}>
      <TabHeader onPressBack={onPressBack} title={title} />
      {webViewUrl && isValidHttpUrl(webViewUrl) ? (
        <WebView
          incognito={false}
          cacheEnabled={false}
          ref={webviewRef}
          source={{ uri: webViewUrl }}
          onLoad={onWebViewLoad}
          decelerationRate={'normal'}
          style={styles.webViewStyle}
          userAgent={CONSTANTS.APP_NAME}
          androidLayerType={'hardware'}
          onNavigationStateChange={onNavigationStateChange}
        />
      ) : (
        <View style={styles.invalidUrlContainer}>
          <Text style={styles.invalidUrlTextStyle}>
            {dictionary?.invalid_url ?? translate('invalid_url')}-
            {webViewUrl ? webViewUrl : ''}
          </Text>
        </View>
      )}
    </SafeAreaView>
  );
};

export default SSOLogout;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  webViewStyle: {
    opacity: 1,
    width: '100%',
    height: ScreenHeight,
  },
  invalidUrlContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  invalidUrlTextStyle: {
    color: COLORS.COLOR_FFFFFF,
    fontSize: normalizeFont(20),
    fontFamily: FONTS.MULISH_BOLD,
  },
});
