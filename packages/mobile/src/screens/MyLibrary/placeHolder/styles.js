import { StyleSheet } from 'react-native';
import {
  normalizeFont,
  normalizeHeight,
  normalizeWidth,
} from 'mobile/src/styles/Mixins';
import { COLORS, FONTS } from '@video-ready/common/utils';

const styles = StyleSheet.create({
  placeHolderContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    height: '100%',
    paddingHorizontal: normalizeWidth(32),
    paddingBottom: normalizeHeight(100),
  },
  placeHolderText: {
    fontSize: normalizeFont(14),
    fontFamily: FONTS.MULISH_REGULAR,
    color: COLORS.COLOR_FFFFFFB2,
    textAlign: 'center',
    marginTop: normalizeHeight(6),
  },
});
export default styles;
