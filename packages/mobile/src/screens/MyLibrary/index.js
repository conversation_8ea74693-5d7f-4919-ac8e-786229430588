import { View } from 'react-native';
import React, { useState, useCallback, memo } from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import TopTabView from './components/TopTabView';
import AppGradient from 'mobile/src/components/AppBackground';
import styles from './styles';
import { MyLibraryTabsData } from './components/TopTabView/data';
import Downloads from './components/Downloads';
import MovieCardListView from './components/MovieCardListView';

/**
 * TabView Component
 *
 * Renders the appropriate content based on the selected tab
 * @param {Object} selectedTab - Currently selected tab
 * @param {Object} props - Props to pass to child components
 * @returns {JSX.Element} Tab content
 */
const TabView = memo(({ selectedTab, props }) => {
  return (
    <View>
      {selectedTab.id === 3 ? (
        <Downloads selectedTab={selectedTab} props={props} />
      ) : (
        <MovieCardListView selectedTab={selectedTab} props={props} />
      )}
    </View>
  );
});

/**
 * MyLibrary Screen Component
 *
 * Main screen for user's library content with tab navigation
 * @param {Object} props - Component props
 * @returns {JSX.Element} MyLibrary screen
 */
const MyLibrary = (props) => {
  // Initialize with the first tab selected by default
  const [selectedTab, setSelectedTab] = useState(MyLibraryTabsData[0]);

  // Memoize the setSelectedTab handler to prevent unnecessary re-renders
  const handleTabChange = useCallback((tab) => {
    setSelectedTab(tab);
  }, []);

  return (
    <SafeAreaView>
      {/* Background gradient */}
      <View style={styles.appGradient}>
        <AppGradient />
      </View>

      {/* Tab navigation */}
      <TopTabView selectedTab={selectedTab} setSelectedTab={handleTabChange} />

      {/* Content based on selected tab */}
      <TabView selectedTab={selectedTab} props={props} />
    </SafeAreaView>
  );
};

export default MyLibrary;
