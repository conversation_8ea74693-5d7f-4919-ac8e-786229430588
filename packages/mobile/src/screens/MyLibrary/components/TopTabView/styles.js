import { StyleSheet } from 'react-native';
import { COLORS } from '@video-ready/common/utils';
import { normalize, normalizeWidth } from 'mobile/src/styles/Mixins';

const styles = StyleSheet.create({
  container: {
    paddingVertical: 8,
  },
  tabList: {
    gap: 8,
    alignItems: 'center',
  },
  tabButton: {
    paddingVertical: 3,
    paddingHorizontal: 10,
    borderRadius: 4,
  },
  tabSelected: {
    borderWidth: 2,
    borderColor: '#FFFFFF80',
    backgroundColor: 'transparent',
  },

  roundedButtonContainer: (selected) => ({
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: normalize(6),
    borderColor: COLORS.COLOR_FFFFFFB3,
    borderWidth: selected ? normalizeWidth(2) : 0,
    paddingHorizontal: normalize(8),
    backgroundColor: selected ? COLORS.COLOR_000000B2 : COLORS.COLOR_FFFFFF1A,
    marginRight: normalizeWidth(8),
  }),
  tabUnselected: {
    borderWidth: 2,
    backgroundColor: '#FFFFFF1A',
    borderColor: 'transparent',
  },
  tabText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '700',
  },
});
export default styles;
