import React, { useCallback } from 'react';
import { FlatList, View } from 'react-native';
import { MyLibraryTabsData } from './data';
import RoundedButton from 'mobile/src/components/Button/RoundedButton';
import styles from './styles';

const TopTabView = ({ selectedTab, setSelectedTab }) => {
  const renderItem = useCallback(
    ({ item }) => {
      const isSelected = selectedTab.id === item.id;

      return (
        <RoundedButton
          title={item.title}
          onPress={() => setSelectedTab(item)}
          customContainerStyle={styles.roundedButtonContainer?.(isSelected)}
        />
      );
    },
    [selectedTab]
  );

  return (
    <View style={styles.container}>
      <FlatList
        data={MyLibraryTabsData}
        renderItem={renderItem}
        keyExtractor={(item) => item.title}
        horizontal
        contentContainerStyle={styles.tabList}
        bounces={false}
        showsHorizontalScrollIndicator={false}
      />
    </View>
  );
};

export default TopTabView;
