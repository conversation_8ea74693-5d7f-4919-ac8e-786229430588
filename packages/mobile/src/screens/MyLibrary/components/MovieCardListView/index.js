import { FlatList, View, Text, TouchableOpacity } from 'react-native';
import React, { useState } from 'react';
import MovieCardListItem from '../MovieCardListItem';
import useTranslate from 'mobile/src/hooks/useTranslate';
import styles from './styles';
import { useMemo } from 'react';
import { useCallback } from 'react';

const MovieCardListView = ({ selectedTab, props }) => {
  const { getText } = useTranslate();
  const [isManageButtonPressed, setManageButtonPressed] = useState(false);

  const listData = useMemo(() => {
    if (selectedTab.id === 1) {
      return [];
    }
    if (selectedTab.id === 2) {
      return [];
    }
    if (selectedTab.id === 4) {
      return [];
    }
    return [];
  }, [selectedTab]);

  const onListItemPress = useCallback(
    (contentId) => {
      if (selectedTab.id === 4) {
        onWatchListListItemPress(contentId);
      }
      if (selectedTab.id === 1) {
        onRecordingListItemPress(contentId);
      }
      if (selectedTab.id === 2) {
        onFutureRecordingListItemPress(contentId);
      }
    },
    [
      selectedTab,
      onRecordingListItemPress,
      onFutureRecordingListItemPress,
      onWatchListListItemPress,
    ]
  );

  const onRecordingListItemPress = useCallback((contentId) => {
    console.log('contentId', contentId);
  }, []);

  const onFutureRecordingListItemPress = useCallback((contentId) => {
    console.log('contentId', contentId);
  }, []);

  const onWatchListListItemPress = useCallback((contentId) => {
    console.log('contentId', contentId);
  }, []);

  const renderItem = ({ item, index }) => {
    return (
      <MovieCardListItem
        item={item}
        onListItemPress={onListItemPress}
        manageDownloadButtonPress={isManageButtonPressed}
      />
    );
  };
  const manageButtonText = useMemo(() => {
    if (selectedTab.id === 1) {
      return getText('manageRecordings');
    }
    if (selectedTab.id === 2) {
      return getText('manageFutureRecordings');
    }
    return getText('manageWatchList');
  }, [selectedTab, getText]);
  return (
    <View style={styles.mainContainer}>
      <FlatList
        data={listData}
        renderItem={renderItem}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>{getText('NoData')}</Text>
          </View>
        }
        bounces={false}
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.flatListContentContainerStyle}
      />
      {listData.length > 0 && (
        <TouchableOpacity
          style={styles.manageButton}
          onPress={() => setManageButtonPressed(!isManageButtonPressed)}
        >
          <Text style={styles.buttonText}> {manageButtonText}</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

export default MovieCardListView;
