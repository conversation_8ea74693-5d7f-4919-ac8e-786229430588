import { StyleSheet } from 'react-native';
import {
  normalizeHeight,
  normalizeWidth,
  normalize,
  normalizeFont,
} from 'mobile/src/styles/Mixins';
import { COLORS, FONTS } from '@video-ready/common/utils';

const styles = StyleSheet.create({
  flatListContentContainerStyle: {
    marginTop: normalizeHeight(20),
    paddingBottom: normalizeHeight(150),
  },
  mainContainer: {
    height: '100%',
  },
  manageButton: {
    borderWidth: normalize(1),
    borderColor: COLORS.COLOR_FFFFFF33,
    position: 'absolute',
    backgroundColor: COLORS.COLORS_0000004D,
    paddingHorizontal: normalizeWidth(16),
    paddingVertical: normalizeWidth(12),
    borderRadius: normalize(8),
    alignSelf: 'center',
    bottom: normalizeHeight(155),
  },
  buttonText: {
    color: COLORS.COLOR_EBEBEB,
    fontSize: normalizeFont(14),
    fontFamily: FONTS.MULISH_SEMIBOLD,
  },
});
export default styles;
