import { StyleSheet } from 'react-native';
import React from 'react';
import Video, { DRMType } from 'react-native-video';
import utils from 'mobile/src/utils';

const OfflinePlayer = ({ route }) => {
  const { videoItem } = route.params;
  console.log('videoItem', videoItem);
  const drm = {
    type: utils?.isIOS ? DRMType.FAIRPLAY : DRMType.WIDEVINE,
    headers: {
      ...(utils?.isIOS && {
        'Content-Type': 'application/x-www-form-urlencoded',
        Accept: 'application/base64',
      }),
    },
    licenseServer: videoItem.mLicenseAcquisitionURL,
    offlineDrm: videoItem.keySetId,
  };

  return (
    <Video
      source={{ uri: videoItem.localUrl, type: 'mpd' }}
      drm={drm}
      style={{ width: '100%', height: '100%' }}
      controls={true}
      resizeMode='contain'
      // offlineDrm='a3NpZDM1OEM2RTND'
      // posterResizeMode="contain"
      // poster="https://www.astrogo.com.my/astrogo/images/astrogo-logo.png"
      rate={1.0}
      volume={1.0}
      muted={false}
      paused={false}
      repeat={false}
      onLoad={() => console.log('onLoad')}
      onProgress={() => console.log('onProgress')}
      onEnd={() => console.log('onEnd')}
      onError={(e) => console.log('onError', e)}
      onBuffer={() => console.log('onBuffer')}
      onTimedMetadata={() => console.log('onTimedMetadata')}
    />
  );
};

export default OfflinePlayer;

const styles = StyleSheet.create({});
