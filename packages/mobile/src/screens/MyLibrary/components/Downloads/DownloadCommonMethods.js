import { useCallback } from 'react';
import CONSTANTS from '@video-ready/common/utils/constant';
import { isWebSeries } from 'mobile/src/utils/commonMethods';
const { DOWNLOAD_STATES } = CONSTANTS;

const useDownloadMethods = () => {
  /**
   *  manageItemClickToBeDelete call on item select for delete from download.
   * isItemSelectedForDelete to check item state selected/unSelected
   * addItemIdsToBeDelete is used for add item in the deleteIDList list
   * removeItemIdsFromDelete is used for remove item/s from the deleteIDList list
   */
  const manageItemClickToBeDelete = useCallback(
    (
      contentId,
      dataList,
      deleteIDList,
      fromEpisodeScreen = false,
      isItemSelectedForDelete
    ) => {
      const currentItem = dataList?.find(
        (item) => item?.contentId === contentId
      );

      if (!isItemSelectedForDelete) {
        return addItemIdsToBeDelete(
          currentItem,
          deleteIDList,
          fromEpisodeScreen
        );
      } else {
        return removeItemIdsFromDelete(
          currentItem,
          deleteIDList,
          fromEpisodeScreen
        );
      }
    },
    [addItemIdsToBeDelete, removeItemIdsFromDelete]
  );

  /**
   * addItemIdsToBeDelete is used for add item in the deleteIDList list
   */
  const addItemIdsToBeDelete = useCallback(
    (currentItem, deleteIDList, fromEpisodeScreen) => {
      if (!fromEpisodeScreen && isWebSeries(currentItem?.contentType)) {
        //  will filter Download status Completed items
        const dowloadCompletedList = currentItem?.episodes?.filter((item) =>
          DOWNLOAD_STATES.isCompleted(item?.dmData?.downloadState)
        );
        const allEpisodesId = dowloadCompletedList?.map(
          (item) => item?.downloadId
        );
        // adding all episodes(for selected series) into deleteIDList;
        return [...deleteIDList, ...allEpisodesId];
      } else {
        return [...deleteIDList, currentItem?.downloadId];
      }
    },
    []
  );

  /**
   * removeItemIdsFromDelete is used for remove item/s from the deleteIDList list
   */
  const removeItemIdsFromDelete = useCallback(
    (currentItem, deleteIDList, fromEpisodeScreen) => {
      if (!fromEpisodeScreen && isWebSeries(currentItem?.contentType)) {
        const allEpisodesId = currentItem?.episodes?.map(
          (item) => item.downloadId
        );
        // removing all episodes(for selected series) from deleteIDList;
        return deleteIDList.filter((id) => !allEpisodesId.includes(id));
      } else {
        return deleteIDList.filter((id) => id !== currentItem?.downloadId);
      }
    },
    []
  );

  /**
   * isIdExistForDelete is used to check is item is already selected for delete
   */
  const isIdExistForDelete = useCallback(
    (item, deleteIDList, fromEpisodeScreen) => {
      if (!fromEpisodeScreen && isWebSeries(item?.contentType)) {
        const hasMatch = item?.episodes.some((data) =>
          deleteIDList.includes(data?.downloadId)
        );
        return hasMatch;
      } else {
        return deleteIDList?.includes(item?.downloadId);
      }
    },
    []
  );

  /**
   * Sorts episodes by season number first, then by episode number within each season
   * For example: S1E1, S1E2, S1E3, S2E1, S2E2, etc.
   */
  const sortedEpisodeList = useCallback((list) => {
    return (
      list?.sort((a, b) => {
        if (a?.seasonNumber !== b?.seasonNumber) {
          return a?.seasonNumber - b?.seasonNumber;
        }
        return a?.episodeNumber - b?.episodeNumber;
      }) || []
    );
  }, []);

  return {
    manageItemClickToBeDelete,
    addItemIdsToBeDelete,
    removeItemIdsFromDelete,
    isIdExistForDelete,
    sortedEpisodeList,
  };
};
export default useDownloadMethods;
