import { StyleSheet } from 'react-native';
import {
  normalizeHeight,
  normalizeWidth,
  normalize,
  normalizeFont,
} from 'mobile/src/styles/Mixins';
import { COLORS, FONTS } from '@video-ready/common/utils';
import { ScreenWidth } from 'mobile/src/utils';

const styles = StyleSheet.create({
  flatListContentContainerStyle: (showDeleteUI) => ({
    marginTop: normalizeHeight(20),
    paddingBottom: showDeleteUI ? normalizeHeight(20) : normalizeHeight(180),
  }),
  contentListMainContainer: { height: '100%' },
  contentListContainer: { flexShrink: 1, flex: 1 },
  mainContainer: {
    height: '100%',
  },
  buttonContainer: {
    width: '100%',
    position: 'absolute',
    bottom: 0,
    alignItems: 'center',
    marginBottom: normalizeHeight(100),
  },
  gradient: {
    width: ScreenWidth,
    height: normalizeHeight(186),
    backgroundColor: 'transparent',
  },
  manageButton: {
    borderWidth: normalize(1),
    borderColor: COLORS.COLOR_FFFFFF33,
    position: 'absolute',
    bottom: 0,
    backgroundColor: COLORS.COLORS_0000004D,
    paddingHorizontal: normalizeWidth(16),
    paddingVertical: normalizeWidth(12),
    borderRadius: normalize(8),
    marginBottom: normalizeHeight(50),
  },
  buttonText: {
    color: COLORS.COLOR_EBEBEB,
    fontSize: normalizeFont(14),
    fontFamily: FONTS.MULISH_SEMIBOLD,
  },
  closeButtonStyle: {
    borderRadius: normalizeWidth(8),
    backgroundColor: COLORS.COLOR_0000004D,
    borderWidth: normalizeWidth(1),
    borderColor: COLORS.COLOR_FFFFFF33,
  },
  closeTextStyle: {
    fontSize: normalizeFont(16),
    fontFamily: FONTS.MULISH_BOLD,
    color: COLORS.COLOR_EBEBEB,
  },
  disablesCloseTextStyle: {
    fontSize: normalizeFont(16),
    fontFamily: FONTS.MULISH_BOLD,
    color: COLORS.COLOR_EBEBEB,
    opacity: 0.5,
  },
});
export default styles;
