import React, { useMemo, useCallback, useState, useEffect } from 'react';
import { View, FlatList, BackHandler } from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import AppGradient from 'mobile/src/components/AppBackground';
import HeaderWithBackIcon from 'mobile/src/components/HeaderWithBackIcon';
import MovieCardListItem from 'mobile/src/screens/MyLibrary/components/MovieCardListItem';
import useDownloadMethods from '../DownloadCommonMethods';
import { goToOfflinePlayer } from '..';
import styles from './styles';

/**
 * DownloadedEpisodesPage Component
 *
 * This component displays a list of downloaded episodes for a specific series.
 * It sorts the episodes by season and episode number, and renders them in a FlatList.
 *
 * @component
 * @returns {JSX.Element} The rendered DownloadedEpisodesPage component
 */
const DownloadedEpisodesPage = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const { manageItemClickToBeDelete, sortedEpisodeList } = useDownloadMethods();
  /**
   * Extracts episodesList and title from route params
   */
  const { episodesList, title, deleteIDList, isManageButtonPressed } = useMemo(
    () => ({
      episodesList: route.params?.episodesList,
      title: route.params?.title,
      deleteIDList: route.params?.deleteIDList,
      isManageButtonPressed: route.params?.isManageButtonPressed,
    }),
    [route.params]
  );
  const [episodeDeleteIDList, setEpisodeDeleteIDList] = useState([
    ...deleteIDList,
  ]);

  const [sortedList, setSortedList] = useState(
    sortedEpisodeList(episodesList) || []
  );

  /**
   * on back press pass updated episodeDeleteIDList on the previous screen.
   */
  const handleBack = useCallback(() => {
    if (route.params?.onGoBack) {
      route.params.onGoBack(episodeDeleteIDList);
    }
    navigation.goBack();
  }, [episodeDeleteIDList, navigation, route.params]);

  // android hardware back press handling
  useEffect(() => {
    const backAction = () => {
      handleBack();
      return true; // prevent default behavior (going back)
    };

    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction
    );

    return () => backHandler.remove(); // cleanup on unmount
  }, [handleBack]);

  /**
   * Handles the press event on a list item
   * @param {string} contentId - The ID of the selected content
   */
  const onListItemPress = useCallback(
    (contentData) => {
      //TODO redirect to Player
      goToOfflinePlayer(contentData, navigation);
    },
    [navigation]
  );

  /**
   * Remove item from sortedList
   *  */
  const onRemoveFromList = useCallback(
    (downloadId) => {
      const updatedSortedList = sortedList?.filter(
        (item) => item?.downloadId !== downloadId
      );
      setSortedList(updatedSortedList);
    },
    [sortedList]
  );

  // if no any episode after cancel download it will be redirect to previous screen
  useEffect(() => {
    if (sortedList?.length === 0) {
      handleBack();
    }
  }, [sortedList, handleBack]);

  /**
   * Renders an individual item in the FlatList
   * @param {Object} param0 - The item to render
   * @param {Object} param0.item - The episode data
   * @returns {JSX.Element} A MovieCardListItem component
   */
  const renderItem = useCallback(
    ({ item }) => {
      return (
        <MovieCardListItem
          item={item}
          fromEpisodePage={true}
          onListItemPress={onListItemPress}
          itemSelectForDelete={itemSelectForDelete}
          deleteIDList={episodeDeleteIDList}
          onRemove={onRemoveFromList}
          manageDownloadButtonPress={isManageButtonPressed}
        />
      );
    },
    [
      onListItemPress,
      itemSelectForDelete,
      episodeDeleteIDList,
      onRemoveFromList,
      isManageButtonPressed,
    ]
  );

  /**
   * on Episode select/unselect for delete
   */
  const itemSelectForDelete = useCallback(
    (contentId, isItemSelectedForDelete) => {
      setEpisodeDeleteIDList(
        manageItemClickToBeDelete(
          contentId,
          sortedList,
          episodeDeleteIDList,
          true,
          isItemSelectedForDelete
        )
      );
    },
    [episodeDeleteIDList, manageItemClickToBeDelete, sortedList]
  );

  return (
    <View style={styles.mobileContainer}>
      <View style={styles.appGradient}>
        <AppGradient />
      </View>

      <HeaderWithBackIcon
        title={title}
        onBackPress={() => {
          handleBack();
        }}
      />

      <FlatList
        style={styles.flatListStyle}
        data={sortedList}
        renderItem={renderItem}
        bounces={false}
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.flatListContentContainerStyle}
        keyExtractor={(item) => item?.contentId}
      />
    </View>
  );
};

export default DownloadedEpisodesPage;
