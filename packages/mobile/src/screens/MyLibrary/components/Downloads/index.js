import { FlatList, View, Text, TouchableOpacity } from 'react-native';
import React, {
  useState,
  useRef,
  useEffect,
  useMemo,
  useCallback,
} from 'react';
import MovieCardListItem from '../MovieCardListItem';
import useTranslate from 'mobile/src/hooks/useTranslate';
import LocalStorageService from 'mobile/src/utils/LocalStorageService';
import DownloadManager from 'mobile/src/downloadplayer/utils/DownloadManager';
import { AsyncStorageKey } from 'mobile/src/utils';
import PlaceHolder from 'mobile/src/screens/MyLibrary/placeHolder';
import { DownloadIconPlaceHolder } from '@video-ready/common/utils/imageConstants';
import LinearGradient from 'react-native-linear-gradient';
import { COLORS } from '@video-ready/common/utils';
import { isWebSeries } from 'mobile/src/utils/commonMethods';
import { useNavigation } from '@react-navigation/native';
import Routenames from 'mobile/src/navigation/routeName';
import { applyCustomToastForWatchlist } from 'mobile/src/components/Toast';
import ManageDownloadView from '../ManageDowonloadView';
import useDownloadMethods from './DownloadCommonMethods';
import { useIsFocused } from '@react-navigation/native';
import styles from './styles';
/**
 * Downloads Component
 *
 * Displays and manages downloaded content in the My Library screen
 * @param {Object} selectedTab - Currently selected tab information
 * @param {Object} props - Component props passed from parent
 * @returns {JSX.Element} Downloads component
 */
const Downloads = ({ selectedTab, props }) => {
  const { getText } = useTranslate();

  const navigation = useNavigation();
  const [isManageButtonPressed, setManageButtonPressed] = useState(false);
  const idmData = useRef([]);
  const [downloadedContent, setDownloadedContent] = useState([]);
  const isFocused = useIsFocused();
  const [deleteIDList, setDeleteIDList] = useState([]);
  const { manageItemClickToBeDelete } = useDownloadMethods();

  /**
   * Memoized gradient colors array for background gradient
   * Uses three shades of the base color (transparent, semi-transparent, and solid)
   */
  const gradientColor = useMemo(
    () => [COLORS.COLOR_11141D00, COLORS.COLOR_11141DE5, COLORS.COLOR_11141D],
    []
  );

  /**
   * Filters downloads to grouping the Series->Episodes into seasons(1 item with right arrow).
   * @param {Array} downloads - Array of downloaded content items
   * @returns {Array} Filtered list with unique seasons for web series and all other content
   * example : [{item1.., type: 'movie'}, {item2..., type:'Seris', episodes:[{item1}, {item2}]}, {item3}]
   */
  const filterDownloads = useCallback((downloads) => {
    const filteredList = [];
    const seriesMap = new Map();
    downloads?.forEach((item) => {
      const { contentType, seriesId } = item;
      if (isWebSeries(contentType)) {
        if (!seriesMap.has(seriesId)) {
          seriesMap.set(seriesId, {
            ...item,
            episodes: [],
          });
          seriesMap.get(seriesId);
          filteredList.push(seriesMap.get(seriesId));
        }
        seriesMap.get(seriesId).episodes.push({ ...item });
      } else {
        filteredList.push(item);
      }
    });
    return filteredList;
  }, []);

  /**
   * get download item from local storage and set into state
   */
  const getDownloads = async () => {
    const downloads = await DownloadManager.getDownloadsFromLocalStorage(true);
    setDownloadedContent(downloads);
  };
  // Initialize component by fetching download data
  useEffect(() => {
    // fetchDownloadedDataFromLocalStorage();
    // DownloadManager.getAllDownloads(getAllDownloadFromIdmCallBack);
    const setDownloads = async () => {
      getDownloads();
    };
    setDownloads();
  }, [filterDownloads, isFocused]);

  const filteredDdownloadedContent = useMemo(
    () => filterDownloads(downloadedContent),
    [downloadedContent, filterDownloads]
  );
  /**
   * Handles navigation to the offline player when a content item is pressed
   * @param {string} contentId - ID of the selected content
   */
  const onListItemPress = useCallback(
    (contentData, isItemWebSeries = false) => {
      if (isItemWebSeries) {
        if (contentData?.episodes.length > 0) {
          navigation?.navigate(Routenames.DOWNLOADED_EPISODE_PAGE, {
            episodesList: contentData?.episodes,
            title: contentData?.seriesTitle,
            deleteIDList: deleteIDList,
            isManageButtonPressed: isManageButtonPressed,
            onGoBack: (value) => {
              //updating deleteIDList on the basis of Episode selection
              setDeleteIDList(value);
            },
          });
        }
      } else {
        // Navigate to the offline player for non-web series content
        goToOfflinePlayer(contentData, navigation);
      }
    },
    [deleteIDList, isManageButtonPressed, navigation]
  );

  /**
   * on movies/series select/unselect for delete
   */
  const itemSelectForDelete = useCallback(
    (contentId, isItemSelectedForDelete) => {
      setDeleteIDList(
        manageItemClickToBeDelete(
          contentId,
          filteredDdownloadedContent,
          deleteIDList,
          false,
          isItemSelectedForDelete
        )
      );
    },
    [deleteIDList, filteredDdownloadedContent, manageItemClickToBeDelete]
  );

  /**
   * Toggle manage button state
   */
  const toggleManageButton = useCallback(() => {
    setManageButtonPressed((prevState) => !prevState);
  }, []);
  /**
   * OnDeletePress for delete download items
   */
  const OnDeletePress = useCallback(async () => {
    // deleteting selected ids
    for (let i = 0; i < deleteIDList?.length; i++) {
      await DownloadManager.removeItemFromLocalStorage(deleteIDList[i]);
    }
    // reset Its State
    resetDownloadState();
    getDownloads();
    // TODO: Call DownloadManager.deleteDownloads() with selected content IDs
    // TODO: Update local storage after successful deletion
    // Toast Msge after depwnload deleted
    applyCustomToastForWatchlist(
      `${deleteIDList?.length} ${getText('items')} ${getText('deleted')}`,
      null,
      'watchListToast'
    );
  }, [getText, resetDownloadState, deleteIDList]);

  /**
   * Resets the download management state
   * - Disables manage mode by setting button pressed state to false
   * - Clears the selected item count
   * - Unchecks all items in the filtered downloaded content list
   * @returns {void}
   */
  const resetDownloadState = useCallback(() => {
    setManageButtonPressed(false);
    setDeleteIDList([]);
  }, []);

  /**
   * Resets the download state when the manage dialog is canceled
   * @returns {void}
   */
  const onManageDialogCancel = useCallback(() => {
    resetDownloadState();
  }, [resetDownloadState]);

  /**
   * Renders an individual downloaded content item
   * Memoized to prevent unnecessary re-renders
   */
  const renderItem = useCallback(
    ({ item }) => {
      return (
        <MovieCardListItem
          item={item}
          onListItemPress={onListItemPress}
          itemSelectForDelete={itemSelectForDelete}
          manageDownloadButtonPress={isManageButtonPressed}
          onRemove={onRemove}
          deleteIDList={deleteIDList}
        />
      );
    },
    [
      onListItemPress,
      itemSelectForDelete,
      isManageButtonPressed,
      onRemove,
      deleteIDList,
    ]
  );
  const onRemove = useCallback(
    (downloadId) => {
      setDownloadedContent(
        downloadedContent.filter((item) => item?.downloadId !== downloadId)
      );
    },
    [downloadedContent]
  );
  const manageButtonText = useMemo(() => getText('manageDownloads'), [getText]);

  /**
   * Renders the content list based on downloaded content availability
   * If content exists, displays a FlatList of downloaded items
   * If no content, shows a placeholder component
   * Memoized to prevent unnecessary re-renders
   * @returns {JSX.Element} FlatList of downloads or placeholder
   */
  const contentList = useMemo(
    () =>
      filteredDdownloadedContent?.length > 0 ? (
        <View style={styles.contentListMainContainer}>
          <View style={styles.contentListContainer}>
            <FlatList
              data={filteredDdownloadedContent}
              renderItem={renderItem}
              ListEmptyComponent={
                <View style={styles.emptyContainer}>
                  <Text style={styles.emptyText}>{getText('NoData')}</Text>
                </View>
              }
              bounces={false}
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.flatListContentContainerStyle(
                isManageButtonPressed
              )}
              keyExtractor={(item) => item?.contentId}
            />
          </View>
          {isManageButtonPressed && (
            <ManageDownloadView
              selectedItemCount={deleteIDList.length}
              onManageDialogCancel={onManageDialogCancel}
              OnDeletePress={OnDeletePress}
            />
          )}
        </View>
      ) : (
        <PlaceHolder
          text={getText('downloadlaceHolderText')}
          placeHolderImage={<DownloadIconPlaceHolder />}
        />
      ),
    [
      filteredDdownloadedContent,
      renderItem,
      getText,
      isManageButtonPressed,
      deleteIDList.length,
      onManageDialogCancel,
      OnDeletePress,
    ]
  );

  /**
   * Renders the manage downloads button if there is downloaded content
   * Memoized to prevent unnecessary re-renders
   * @returns {JSX.Element|null} Manage downloads button or null if no content
   */
  const renderManageDownloadButton = useMemo(() => {
    return downloadedContent?.length > 0 && !isManageButtonPressed ? (
      <View style={styles.buttonContainer}>
        <View>
          <LinearGradient
            style={styles.gradient}
            locations={[0, 0.56, 1]}
            colors={gradientColor}
            useAngle={true}
            angle={180}
            pointerEvents='none'
          />
        </View>
        <TouchableOpacity
          style={styles.manageButton}
          onPress={toggleManageButton}
        >
          <Text style={styles.buttonText}>{manageButtonText}</Text>
        </TouchableOpacity>
      </View>
    ) : null;
  }, [
    downloadedContent?.length,
    gradientColor,
    manageButtonText,
    toggleManageButton,
    isManageButtonPressed,
  ]);

  return (
    <View style={styles.mainContainer}>
      {contentList}
      {renderManageDownloadButton}
    </View>
  );
};

export default React.memo(Downloads);

export const goToOfflinePlayer = (contentData, navigation) => {
  navigation?.navigate(Routenames.FULLSCREEN_NEW_PLAYER, {
    railId: contentData?.railId,
    coverImage: contentData?.coverImage,
    playTrailer: false,
    contentTitle: contentData?.contentTitle,
    watchHistory: {},
    contentId: contentData?.contentId,
    contentType: contentData?.contentType,
    seriesData: [],
    offlineVod: true,
    downloadId: contentData?.downloadId,
  });
};
