import React, { useCallback, useMemo } from 'react';
import { Text, View } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Animated, { FadeIn, FadeOut } from 'react-native-reanimated';
import { COLORS } from '@video-ready/common/utils';
import MaskedView from '@react-native-masked-view/masked-view';
import RoundButton from 'mobile/src/components/RoundButton';
import useTranslate from 'mobile/src/hooks/useTranslate';
import CommonModal from 'mobile/src/components/Modal/CommonModal';
import { ModalType } from 'mobile/src/appConstants';
import styles from './styles';

/**
 * ManageDownloadView Component
 *
 * This component provides a UI for managing downloaded content.
 * It displays the number of selected items and provides options to delete or cancel the operation.
 *
 * @param {Object} props - Component props
 * @param {number} props.selectedItemCount - Number of items selected for deletion
 * @param {Function} props.onManageDialogCancel - Callback function to cancel the manage dialog
 * @param {Function} props.OnDeletePress - Callback function to handle delete action
 *
 * @returns {JSX.Element} Rendered ManageDownloadView component
 */
const ManageDownloadView = ({
  selectedItemCount,
  onManageDialogCancel,
  OnDeletePress,
}) => {
  const { getText } = useTranslate();

  /**
   * Handles the confirmation delete button press
   * Opens a confirmation modal to verify delete action
   * @returns {void}
   */
  const onPressConfirmDelete = useCallback(() => {
    const openConfirmationModal = () => {
      CommonModal?.show({
        containerStyle: styles.containerTopPadding,
        confirmButtonText: getText('deleteAll'),
        closeButtonText: getText('cancel'),
        onPressClose: () => {
          CommonModal?.hide();
        },
        onPressConfirm: () => {
          console.log('onPressConfirm>>');
          CommonModal?.hide();
          OnDeletePress();
        },
        title: `${getText('delete')} ${selectedItemCount} ${getText(
          'items'
        )}${getText('qustionmark')}`,
        description: `${getText(
          'deleteSubTitleTextFirst'
        )} ${selectedItemCount} ${getText('deleteSubTitleTextSecond')}`,
        modalType: ModalType.LOGIN,
        closeButtonStyle: styles.closeButtonStyle,
        closeTextStyle: styles.closeTextStyle,
        descriptionTextStyle: styles.subTitle,
        closeOnBackPress: true,
        highlighted: false,
        noSelectedButton: true,
      });
    };
    openConfirmationModal();
  }, [getText, selectedItemCount, OnDeletePress]);

  /**
   * Renders the gradient background for the manage download view
   * @returns {JSX.Element} Gradient background components
   */
  const renderGradient = useMemo(() => {
    return (
      <>
        {/* Main background gradient */}
        <LinearGradient
          colors={[COLORS.COLOR_0E2044, COLORS.COLOR_0E2044]}
          style={styles.gradientStyle}
          start={{ x: 0, y: 0 }}
          end={{ x: 0, y: 1 }}
        />
        {/* Top-left to center gradient overlay */}
        <LinearGradient
          colors={[COLORS.COLOR_3776B145, COLORS.COLOR_3776B100]}
          style={styles.gradientStyle}
          start={{ x: 0, y: 0 }}
          end={{ x: 0.5, y: 0.5 }}
        />
        {/* Top-right corner gradient overlay */}
        <LinearGradient
          colors={[COLORS.COLOR_EA018000, COLORS.COLOR_EA018014]}
          style={styles.gradientStyle}
          start={{ x: 0.5, y: 0 }}
          end={{ x: 1, y: 0 }}
        />
        {/* Masked view for border gradient effect */}
        <MaskedView
          style={styles.maskedView}
          maskElement={<View style={styles.maskElementBorder} />}
        >
          <LinearGradient
            colors={[COLORS.COLOR_FFFFFF33, COLORS.COLOR_00000000]}
            style={styles.backgroundGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 0, y: 1 }}
          />
        </MaskedView>
      </>
    );
  }, []);

  /**
   * Renders the download management UI
   * @returns {JSX.Element} Download management UI components
   */
  const renderDownloadManagement = useMemo(() => {
    return (
      <View style={styles.downloadContainer}>
        {/* Header bar for the bottom sheet */}
        <View style={styles.bottomSheetHeaderBar} />
        {/* Container for displaying the number of selected items */}

        <View style={styles.selectedTextcontainer}>
          <Text style={styles.selectedItemText}>
            {selectedItemCount} {getText('itemSelected')}
          </Text>
        </View>

        {/* Button to delete selected items */}
        <RoundButton
          disabled={selectedItemCount === 0}
          onPress={onPressConfirmDelete}
          text={getText('deleteSelectedItem')}
          style={[styles.closeButtonDefaultStyle(), styles.closeButtonStyle]}
          textStyle={[
            styles.closeTextDefaultStyle,
            selectedItemCount
              ? styles.closeTextStyle
              : styles.disablesCloseTextStyle,
          ]}
          showSelected={false}
        />

        {/* Button to cancel the manage dialog */}
        <RoundButton
          onPress={onManageDialogCancel}
          text={getText('cancel')}
          style={[styles.closeButtonDefaultStyle(), styles.closeButtonStyle]}
          textStyle={[styles.closeTextDefaultStyle, styles.closeTextStyle]}
          showSelected={false}
        />
      </View>
    );
  }, [getText, onManageDialogCancel, onPressConfirmDelete, selectedItemCount]);

  // Render the main component with animated fade in/out transitions
  // Displays the gradient background and download management UI
  return (
    <Animated.View
      entering={FadeIn}
      exiting={FadeOut}
      style={styles.contentParent}
    >
      {renderGradient}
      {renderDownloadManagement}
    </Animated.View>
  );
};

export default ManageDownloadView;
