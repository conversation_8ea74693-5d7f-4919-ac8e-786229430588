import { StyleSheet } from 'react-native';

import {
  normalize,
  normalizeFont,
  normalizeHeight,
  normalizeWidth,
} from 'mobile/src/styles/Mixins';
import { COLORS, FONTS } from '@video-ready/common/utils';

const styles = StyleSheet.create({
  gestureHandlerRootViewStyle: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  containerTopPadding: {
    paddingTop: normalizeHeight(10),
  },
  bottomSheetHeaderBar: {
    alignSelf: 'center',
    width: normalizeWidth(48),
    height: normalizeHeight(4),
    borderRadius: normalizeWidth(3),
    marginBottom: normalizeHeight(18),
    backgroundColor: COLORS.COLOR_FFFFFF80,
  },
  gradientStyle: {
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    position: 'absolute',
    borderTopLeftRadius: normalize(8),
    borderTopRightRadius: normalize(8),
  },
  title: {
    color: COLORS.COLOR_F2F2F7,
    fontSize: normalizeFont(20),
    fontFamily: FONTS.ROBOTO_BOLD,
    lineHeight: normalizeHeight(30),
    marginBottom: normalizeHeight(8),
  },
  selectedTextcontainer: { width: '100%' },
  selectedTextcontainerRow: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectedItemText: {
    color: COLORS.COLOR_FFFFFFB3,
    fontSize: normalizeFont(16),
    fontFamily: FONTS.MULISH_BOLD,
    marginBottom: normalizeHeight(8),
    textAlign: 'center',
  },
  subTitle: {
    color: COLORS.COLOR_FFFFFFB2,
    fontSize: normalizeFont(14),
    fontFamily: FONTS.MULISH_REGULAR,
    marginBottom: normalizeHeight(8),
  },
  closeButtonDefaultStyle: (forLogin, width, isFold) => ({
    width: '100%',
    height: normalizeHeight(44),
    marginTop: normalizeHeight(8),
    backgroundColor: forLogin ? COLORS.TRANSPARENT : COLORS.COLOR_66666680,
    borderRadius: normalizeHeight(8),
    alignSelf: 'center',
  }),
  closeTextDefaultStyle: {
    color: COLORS.COLOR_F2F2F7,
  },
  maskedView: {
    width: '100%',
    height: '100%',
    position: 'absolute',
  },
  backgroundGradient: {
    width: '100%',
    height: '100%',
  },

  maskElementBorder: {
    width: '100%',
    height: '100%',
    borderTopLeftRadius: normalizeWidth(8),
    borderTopRightRadius: normalizeWidth(8),
    borderWidth: normalize(1),
  },
  downloadContainer: {
    paddingHorizontal: normalizeWidth(16),
    paddingTop: normalizeWidth(10),
    paddingBottom: normalizeWidth(32),
  },

  contentParent: {
    width: '100%',
    flexDirection: 'column',
    marginBottom: normalizeHeight(140),
  },
  closeButtonStyle: {
    borderRadius: normalizeWidth(8),
    backgroundColor: COLORS.COLOR_0000004D,
    borderWidth: normalizeWidth(1),
    borderColor: COLORS.COLOR_FFFFFF33,
  },
  closeTextStyle: {
    fontSize: normalizeFont(16),
    fontFamily: FONTS.MULISH_BOLD,
    color: COLORS.COLOR_EBEBEB,
  },
  disablesCloseTextStyle: {
    fontSize: normalizeFont(16),
    fontFamily: FONTS.MULISH_BOLD,
    color: COLORS.COLOR_EBEBEB,
    opacity: 0.5,
  },
});

export default styles;
