import { StyleSheet } from 'react-native';
import {
  normalizeWidth,
  normalizeHeight,
  normalizeFont,
  normalize,
} from 'mobile/src/styles/Mixins';
import { COLORS, FONTS } from '@video-ready/common/utils';
import utils from 'mobile/src/utils';

const styles = StyleSheet.create({
  parentContainer: {
    marginBottom: normalizeHeight(24),
    paddingHorizontal: normalizeHeight(12),
  },
  mainContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  cardContainer: {
    flex: 1,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  posterContainer: {
    width: utils?.isTab ? normalizeWidth(176) : normalizeWidth(107),
    height: utils?.isTab ? normalizeWidth(99) : normalizeHeight(60),
    marginRight: utils?.isTab ? normalizeWidth(16) : normalizeWidth(8),
  },
  posterImage: {
    width: '100%',
    height: '100%',
    borderRadius: normalizeWidth(8),
    shadowRadius: normalizeWidth(10),
    elevation: normalize(10),
    shadowColor: COLORS.COLORS_0000004D,
    shadowOffset: {
      width: 0,
      height: 0,
    },
  },
  contentContainer: {
    flex: 1,
  },
  expireRow: {
    flexDirection: 'row',
    marginBottom: normalizeWidth(4),
    alignItems: 'center',
  },
  expireBadge: {
    backgroundColor: COLORS.COLOR_EBEBEB,
    borderRadius: normalize(3),
    paddingVertical: normalizeHeight(2),
    paddingHorizontal: normalizeWidth(4),
    marginRight: 4,
    alignItems: 'center',
    justifyContent: 'center',
    shadowRadius: normalize(3),
    elevation: normalize(3),
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowColor: COLORS.COLOR_00000040,
  },
  expireBadgeText: {
    color: COLORS.COLOR_010414,
    fontFamily: FONTS.MULISH_BOLD,
    fontSize: normalizeFont(10),
  },
  watchByContainer: {
    flex: 1,
  },
  watchByText: {
    color: COLORS.COLOR_FFFFFFB3,
    fontFamily: FONTS.MULISH_SEMIBOLD,
    fontSize: normalizeFont(10),
  },
  watchByValueText: {
    color: COLORS.COLOR_FFFFFFB3,
    fontSize: normalizeFont(10),
    fontFamily: FONTS.MULISH_EXTRABOLD,
    flexShrink: 1,
  },
  titleText: {
    color: COLORS.COLOR_FFFFFF,
    fontSize: utils?.isTab ? normalizeFont(20) : normalizeFont(14),
    fontFamily: FONTS.MULISH_BOLD,
    textShadowColor: COLORS.COLORS_0000004D,
    textShadowOffset: {
      width: 0,
      height: normalize(2),
    },
    textShadowRadius: normalize(4),
  },
  metaContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  metaBadge: {
    backgroundColor: COLORS.COLOR_FFFFFF1A,
    paddingHorizontal: normalizeWidth(4),
    paddingVertical: normalizeHeight(2),
    borderRadius: normalize(3),
    marginRight: normalizeWidth(8),
    marginTop: normalizeHeight(6),
    shadowColor: COLORS.COLORS_0000004D,
    shadowOffset: {
      width: 0,
      height: normalize(1),
    },
    shadowRadius: normalize(3),
    elevation: normalize(3),
    shadowOpacity: 1,
  },
  metaText: {
    color: COLORS.COLORS_EBEBEB,
    fontSize: normalizeFont(10),
    fontFamily: FONTS.MULISH_SEMIBOLD,
  },
  metaTextBold: {
    color: COLORS.COLOR_F2F2F7,
    fontSize: normalizeFont(11),
    fontFamily: FONTS.MULISH_EXTRABOLD,
  },
  checkBoxContainer: {
    paddingEnd: normalizeWidth(8),
  },
  rightArrowContainer: {
    marginLeft: normalizeWidth(4),
  },
  contentLogoView: {
    width: utils.isTab ? normalizeWidth(100) : normalizeWidth(46),
    height: utils.isTab ? normalizeWidth(43) : normalizeWidth(20),
    position: 'absolute',
    bottom: normalizeHeight(5),
    right: normalizeWidth(5),
    zIndex: 1,
  },
  synopsisContainer: (manageDownloadButtonPress) => ({
    paddingStart: manageDownloadButtonPress
      ? normalizeWidth(24)
      : normalizeWidth(0),
  }),
});

export default styles;
