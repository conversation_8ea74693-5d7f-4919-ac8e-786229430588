import React, { useCallback, useMemo, useState } from 'react';
import { Image, Platform, Text, TouchableOpacity, View } from 'react-native';
import {
  DownloadCheckBoxSelected,
  DownloadCheckBoxEmpty,
  RightArrow,
} from '@video-ready/common/utils/imageConstants';
import { normalize, normalizeWidth } from 'mobile/src/styles/Mixins';
import utils from 'mobile/src/utils';
import { parseListToString } from 'mobile/src/screens/ContentPage/helper';
import useTranslate from 'mobile/src/hooks/useTranslate';
import ContentLogo from 'mobile/src/components/ContentLogo';
import { isWebSeries } from 'mobile/src/utils/commonMethods';
import Animated, { FadeInLeft, FadeOutLeft } from 'react-native-reanimated';
import DownloadStatusButton from 'mobile/src/downloadplayer/DownloadStatusButton';
import CONSTANTS from '@video-ready/common/utils/constant';
import useDownloadMethods from '../Downloads/DownloadCommonMethods';
import useDownloadCardRenderMethods from 'mobile/src/hooks/useDownloadCardRenderMethods';
import CardSynopsis from 'mobile/src/components/CardSynopsis';
import styles from './styles';

const { DOWNLOAD_STATES } = CONSTANTS;

const MovieCardListItem = ({
  onListItemPress,
  itemSelectForDelete,
  manageDownloadButtonPress = false,
  item,
  fromEpisodePage = false,
  onRemove,
  deleteIDList,
}) => {
  const [isDownloaded, setIsDownloaded] = useState(
    DOWNLOAD_STATES.isCompleted(item?.dmData?.downloadState) || false
  );
  const { getText } = useTranslate();
  const { webSeriesTags, episodeMoviesItemTags } =
    useDownloadCardRenderMethods();

  const contentIsWebSeries = useMemo(() => {
    return isWebSeries(item?.contentType);
  }, [item?.contentType]);

  /**
   * Determines content is on Download Page & content is webSeries
   * @returns {boolean}
   */
  const isDowloadPageWithWebSeries = useMemo(() => {
    return !fromEpisodePage && contentIsWebSeries;
  }, [fromEpisodePage, contentIsWebSeries]);

  const { isIdExistForDelete } = useDownloadMethods();

  //TODO add logic for isExpireSoon, watchBy, recordedOn
  const isExpireSoon = useMemo(() => {});
  const watchBy = useMemo(() => {});
  const recordedOn = useMemo(() => {});
  const [loadMore, setLoadMore] = useState();

  /**
   * Renders the tags for the movie or web series
   * For web series, it shows season and episode numbers or total episodes
   * For movies, it shows the duration
   * Also includes genre and rating information if available
   * @returns {JSX.Element} A View component containing the tags
   */
  const renderTags = useMemo(() => {
    const tagList = [];
    // on Download page && isWebSeries == true
    if (isDowloadPageWithWebSeries) {
      webSeriesTags(item, tagList);
    } else {
      episodeMoviesItemTags(item, tagList, fromEpisodePage);
    }

    return (
      <View style={styles.metaContainer}>
        {tagList.map((tagItem) => (
          <View style={styles.metaBadge}>
            <Text
              style={
                tagItem?.textStyleBold === true
                  ? styles.metaTextBold
                  : styles.metaText
              }
            >
              {tagItem?.text}
            </Text>
          </View>
        ))}
      </View>
    );
  }, [
    isDowloadPageWithWebSeries,
    fromEpisodePage,
    item,
    webSeriesTags,
    episodeMoviesItemTags,
  ]);

  /**
   * Renders the expiry soon or recorded on view
   * If the content is expiring soon, it shows an expiry badge and watch by date
   * If the content is recorded, it shows the recorded on date
   * @returns {JSX.Element|null} A View component for expiry or recorded information, or null if neither applies
   */
  const renderExpirySoonRecordedOnView = useMemo(() => {
    if (isExpireSoon && watchBy) {
      return (
        <View style={styles.expireRow}>
          <View style={styles.expireBadge}>
            <Text style={styles.expireBadgeText}>{getText('expireSoon')}</Text>
          </View>
          <View style={styles.watchByContainer}>
            <Text numberOfLines={2} style={styles.watchByText}>
              {getText('watchBy')}
              <Text style={styles.watchByValueText}> {watchBy}</Text>
            </Text>
          </View>
        </View>
      );
    } else if (recordedOn) {
      return (
        <Text numberOfLines={2} style={styles.watchByText}>
          {getText('recordedOn')}
          <Text style={styles.watchByValueText}> {recordedOn}</Text>
        </Text>
      );
    }
    return null;
  }, [getText, isExpireSoon, recordedOn, watchBy]);

  /**
   * Renders the poster image for the content item
   * If boxCoverImage or coverImage exists, displays the image with optional logo overlay
   * If no image exists, returns an empty container
   * Memoized to prevent unnecessary re-renders
   * @returns {JSX.Element} A View component containing the poster image and logo
   */
  const posterImage = useMemo(() => {
    // Initialize image source variables
    let coverImage = null;
    let coverLogo = null;

    // Set cover image source - use local file if downloaded, otherwise use remote URL
    if (item?.localCoverImage) {
      // Add file:// prefix for Android local files
      coverImage =
        Platform.OS === 'android'
          ? isDowloadPageWithWebSeries
            ? 'file://' + item?.seriesData?.localCoverImage
            : 'file://' + item?.localCoverImage
          : item?.localCoverImage;
    } else if (item?.boxCoverImage ?? item?.coverImage) {
      // Use box cover image if available, otherwise fallback to regular cover image
      coverImage = item?.boxCoverImage ?? item?.coverImage;
    }

    // Set logo image source - use local file if downloaded, otherwise use remote URL
    if (item?.localCoverLogo) {
      // Add file:// prefix for Android local files
      coverLogo =
        Platform.OS === 'android'
          ? 'file://' + item?.localCoverLogo
          : item?.localCoverLogo;
    } else if (item?.boxCoverLogo) {
      coverLogo = item?.boxCoverLogo;
    }
    if (coverImage) {
      return (
        <View style={styles.posterContainer}>
          <Image source={{ uri: coverImage }} style={styles.posterImage} />
          {coverLogo && (
            <ContentLogo
              contentLogo={coverLogo}
              viewStyle={styles.contentLogoView}
            />
          )}
        </View>
      );
    } else {
      return <View style={styles.posterContainer} />;
    }
  }, [
    isDowloadPageWithWebSeries,
    item?.boxCoverImage,
    item?.boxCoverLogo,
    item?.coverImage,
    item?.localCoverImage,
    item?.localCoverLogo,
    item?.seriesData?.localCoverImage,
  ]);

  /**
   * Determines the title text to display based on content type and context
   * For web series not on series page, shows series title
   * Otherwise shows the default title of the content
   * @returns {string} Title text to display
   */
  const titleText = useMemo(() => {
    if (!fromEpisodePage && !contentIsWebSeries) {
      return item?.defaultTitle;
    }
    if (isDowloadPageWithWebSeries) {
      return item?.seriesData?.defaultTitle;
    }
    // fromEpisodePage === true
    return item?.title
      ? item?.title
      : item?.episodeNumber
        ? `E${item.episodeNumber}`
        : item?.defaultTitle;
  }, [
    contentIsWebSeries,
    fromEpisodePage,
    isDowloadPageWithWebSeries,
    item?.defaultTitle,
    item.episodeNumber,
    item?.seriesData?.defaultTitle,
    item?.title,
  ]);

  /**
   * will show the checked/un-checked state for item to be deleted.
   */
  const isItemSelectedForDelete = useMemo(() => {
    return isIdExistForDelete(item, deleteIDList, fromEpisodePage);
  }, [deleteIDList, fromEpisodePage, isIdExistForDelete, item]);

  const renderTickView = useMemo(() => {
    if (isItemSelectedForDelete) {
      return (
        <DownloadCheckBoxSelected
          width={normalizeWidth(16)}
          height={normalizeWidth(16)}
        />
      );
    } else {
      return (
        <DownloadCheckBoxEmpty
          width={normalizeWidth(16)}
          height={normalizeWidth(16)}
        />
      );
    }
  }, [isItemSelectedForDelete]);

  /**
   * on LoadMore handle synopsis visibility
   */
  const onContenPressed = useCallback(() => {
    setLoadMore((prev) => !prev);
  }, []);

  return (
    <View style={styles.parentContainer}>
      <View style={styles.mainContainer}>
        {/* showing checkbox */}
        {manageDownloadButtonPress && (
          <Animated.View entering={FadeInLeft} exiting={FadeOutLeft}>
            <TouchableOpacity
              onPress={() =>
                isDownloaded
                  ? itemSelectForDelete(
                      item?.contentId,
                      isItemSelectedForDelete
                    )
                  : null
              }
            >
              <View style={styles.checkBoxContainer}>{renderTickView}</View>
            </TouchableOpacity>
          </Animated.View>
        )}
        <TouchableOpacity
          style={styles.cardContainer}
          onPress={() => {
            if (isDowloadPageWithWebSeries) {
              onListItemPress?.(item, isDowloadPageWithWebSeries);
              return;
            }
            if (isDownloaded) {
              onListItemPress?.(item);
            }
          }}
        >
          <View style={styles.row}>
            {posterImage}
            <TouchableOpacity
              style={styles.contentContainer}
              onPress={onContenPressed}
            >
              <View>
                {renderExpirySoonRecordedOnView}
                {item?.defaultTitle && (
                  <Text numberOfLines={1} style={styles.titleText}>
                    {titleText}
                  </Text>
                )}
                {renderTags}
              </View>
            </TouchableOpacity>
            {!isDownloaded && !isDowloadPageWithWebSeries && (
              <View style={styles.rightArrowContainer}>
                <DownloadStatusButton
                  contentData={item}
                  isCircleStyle={false}
                  onComplete={() => setIsDownloaded(true)}
                  onRemove={() => onRemove(item?.downloadId)}
                  dmData={item?.dmData}
                />
              </View>
            )}
            {isDowloadPageWithWebSeries && (
              <View style={styles.rightArrowContainer}>
                <RightArrow
                  width={utils.isTab ? normalize(28) : normalize(24)}
                  height={utils.isTab ? normalize(28) : normalize(24)}
                />
              </View>
            )}
          </View>
        </TouchableOpacity>
      </View>
      <CardSynopsis
        confgStyle={styles.synopsisContainer(manageDownloadButtonPress)}
        linesToTruncate={3}
        loadMore={loadMore}
        text={
          isDowloadPageWithWebSeries
            ? item?.seriesData?.description
            : item?.description
        }
        contentData={isDowloadPageWithWebSeries ? item?.seriesData : item}
      />
    </View>
  );
};
export default MovieCardListItem;
