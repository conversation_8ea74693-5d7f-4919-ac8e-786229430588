import { useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import React, { useEffect } from 'react';
import {
  View,
  Text,
  SafeAreaView,
  Image,
  useWindowDimensions,
} from 'react-native';

import { useNavigation } from '@react-navigation/native'; // <-- Add this import
import { GETTING_STARTED_DATA_MOB } from '../../appConstants';
import styles from './styles';
import { normalizeWidth } from '../../styles/Mixins';
import { PairedSuccess } from '@video-ready/common/utils/imageConstants';
import MobileSuccessHeader from 'mobile/src/components/MobileSuccessHeader';
import LinearGradient from 'react-native-linear-gradient';
import { COLORS } from '@video-ready/common/utils';

const LinkingSuccessScreen = () => {
  const navigation = useNavigation();
  const { t: translate } = useTranslation();
  const { height: DynamicHeight, width: DynamicWidth } = useWindowDimensions();
  const dictionary = useSelector((state) => state?.miscellaneous?.dictionary);

  useEffect(() => {
    const timer = setTimeout(() => {
      navigation.goBack();
    }, 3000);
    return () => clearTimeout(timer);
  }, [navigation]);

  return (
    <SafeAreaView style={styles.mainContainer}>
      <View style={styles.headerContainer}>
        <MobileSuccessHeader />
      </View>
      <View style={{ flex: 1 }}>
        <LinearGradient
          style={styles.topGradient}
          colors={[COLORS.COLOR_1A0513, COLORS.COLOR_1A051300]}
        />
        <LinearGradient
          style={styles.bottomGradient}
          locations={[0.5107, 0.9282]}
          start={{ x: 0.5, y: 1 }}
          end={{ x: 0.5, y: 0 }}
          colors={[COLORS.COLOR_1A0513, COLORS.COLORS_1a05134d]}
        />
        <View style={styles.bgImageContainer(DynamicWidth)}>
          <View style={styles.imageCont(DynamicWidth)}>
            <Image
              source={GETTING_STARTED_DATA_MOB.img1}
              resizeMode={'contain'}
              style={styles.movieImage(DynamicHeight, DynamicWidth)}
            />
          </View>
        </View>

        <View style={styles.successViewContainer}>
          <PairedSuccess
            height={normalizeWidth(200)}
            width={normalizeWidth(200)}
          />
          <Text style={styles.pairedSuccessMsgStyle}>
            {dictionary?.paired_success_msg ?? translate('paired_success_msg')}
          </Text>
          <Text style={styles.enjoyWatchingMsgStyle}>
            {dictionary?.enjoy_watching ?? translate('enjoy_watching')}
          </Text>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default LinkingSuccessScreen;
