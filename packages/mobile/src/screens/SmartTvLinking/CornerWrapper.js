// components/CornerWrapper.js
import React from 'react';
import { View } from 'react-native';
import { Corner } from '@video-ready/common/utils/imageConstants';
import styles from './styles'; // Adjust the import path as necessary
const CornerWrapper = ({ rotate = '0deg', positionStyle }) => {
  return (
    <View style={[styles.corner, positionStyle, { transform: [{ rotate }] }]}>
      <Corner width={40} height={40} />
    </View>
  );
};

export default CornerWrapper;
