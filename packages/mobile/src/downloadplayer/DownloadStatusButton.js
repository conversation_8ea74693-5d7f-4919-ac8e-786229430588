import { TouchableOpacity } from 'react-native';
import React, { useCallback, useMemo, useState, useEffect, memo } from 'react';
import { useOfflineDownload } from './progressprovider/DownloadOfflineProvider';
import styles from './styles';
import CONSTANTS from '@video-ready/common/utils/constant';
import useDownloadIcon from './useDownloadIcon';
import DownloadManager from './utils/DownloadManager';
import { fetchEpisodeDataNoDispatch } from '@video-ready/common/services/home/<USER>';
import { isMovie, isWebSeries } from '../utils/commonMethods';
import { useRef } from 'react';
import CommonModal from '../components/Modal/CommonModal';
import { applyCustomToast, ToastType } from 'mobile/src/components/Toast';
import i18next from 'i18next';
import { DownloadManagerChecker } from './utils/DMChecker';
import NetInfo from '@react-native-community/netinfo';
const { DOWNLOAD_STATES } = CONSTANTS;

/**
 * Button component that displays download status and handles download actions
 *
 * This component manages the UI and logic for content downloads, showing different
 * states (pending, downloading, completed, error) and handling user interactions.
 *
 * @param {Object} props - Component props
 * @param {Object} props.contentData - Content data object containing download information
 * @param {boolean} [props.isCircleStyle=true] - Whether to use circular button style
 * @param {Function} [props.onComplete] - Callback when download completes
 * @param {Function} [props.onRemove] - Callback when download is removed
 * @param {Object} [props.dmData] - Pre-fetched download manager data
 * @returns {JSX.Element} Download status button component
 */
const DownloadStatusButton = memo(
  ({
    contentData,
    seriesData,
    isCircleStyle = true,
    onComplete,
    onRemove,
    dmData,
  }) => {
    const { t: translate } = i18next;
    // Flag to prevent initial fetch if dmData is provided
    const doNotFetchFirstTime = useRef(true);

    // Get current download state from context provider
    const { offlineDownloadState: downloadEvent, resetDownloadStates } =
      useOfflineDownload();

    // Local state to track download details
    const [downloadDetails, setDownloadDetails] = useState(null);

    /**
     * Generate unique download ID for content based on profile and content ID
     */
    const downloadId = useMemo(
      () => DownloadManager.getDownloadId(contentData?.contentId),
      [contentData]
    );

    /**
     * Fetch download details when component mounts or download ID changes
     * Uses pre-fetched data if available on first render
     */
    useEffect(() => {
      if (doNotFetchFirstTime.current && dmData) {
        setDownloadDetails(dmData);
        doNotFetchFirstTime.current = false;
        return;
      }
      getDownloadDetails();
    }, [downloadId, getDownloadDetails, dmData]);

    /**
     * Retrieve all downloads from download manager and find matching download details
     * Updates local state with found download or null
     */
    const getDownloadDetails = useCallback(() => {
      DownloadManager.getAllDownloads((downloads) => {
        const downloadDetail = downloads.find(
          (download) => download.name === downloadId
        );

        setDownloadDetails(downloadDetail || null);
      });
    }, [downloadId]);

    /**
     * Determine current download status based on event or stored details
     * Prioritizes real-time events over stored state
     * Triggers completion callback when download is complete
     */
    const downloadStatus = useMemo(() => {
      let status = null;
      if (downloadEvent?.name === downloadId) {
        // Use real-time status from download event if it matches current download
        status = downloadEvent?.status;
      } else {
        // Fall back to stored download state
        status = downloadDetails?.downloadState;
      }

      // Trigger completion callback if download is complete
      if (DOWNLOAD_STATES.isCompleted(status)) {
        onComplete?.();
      }
      return status;
    }, [downloadEvent, downloadDetails, downloadId, onComplete]);

    /**
     * Get appropriate download icon based on status and progress
     * Uses custom hook to determine which icon to display
     */
    const renderDownloadIcon = useDownloadIcon(
      downloadStatus,
      downloadEvent?.percentageComplete
    );

    /**
     * Start download process for content
     * Updates UI immediately to show pending state
     */
    const startDownload = useCallback(
      async (finalContentData) => {
        const cb = (response = true) => {
          if (response) {
            // Update UI after download starts
            requestAnimationFrame(() => {
              getDownloadDetails();
            }, 1000);
            // Show pending state immediately for better UX
            setDownloadDetails({ downloadState: DOWNLOAD_STATES.PENDING });
          }
        };
        DownloadManager.addToDownloadQueue(finalContentData, downloadId, cb);
      },
      [downloadId, getDownloadDetails]
    );

    /**
     * Checks if the content is eligible for download.
     * Performs the following checks in order:
     *   1. Sufficient device storage (2GB minimum, as per DownloadManager).
     *   2. Valid download URL (using DownloadManagerChecker).
     *   3. Active internet connection.
     * If any check fails, shows a modal with a relevant message and options to retry or close.
     * Returns true if all checks pass, false otherwise.
     */
    const checkDownloadEligibility = useCallback(
      async (finalContentData) => {
        let isEligible = true;

        // 1. Check for sufficient storage space
        const isStorageSpaceAvailable =
          await DownloadManager.checkStorageSpace();
        if (!isStorageSpaceAvailable) {
          isEligible = false;
          // Show modal for insufficient storage
          CommonModal?.show({
            title: 'insufficient_storage',
            description: 'insufficient_storage_description',
            confirmButtonText: 'retry',
            closeButtonText: 'close',
            onPressConfirm: () => {
              CommonModal?.hide();
              prepareDownload();
            },
            onPressClose: () => {
              CommonModal?.hide();
            },
          });
        }

        // 2. Check for valid download URL
        if (
          !DownloadManagerChecker.checkDownloadEligibility(
            finalContentData?.playUrlDetail
          )
        ) {
          isEligible = false;
          // Show modal for invalid/broken download URL
          CommonModal?.show({
            title: 'download_unavailable_title',
            description: 'download_unavailable_description',
            confirmButtonText: 'retry',
            closeButtonText: 'close',
            onPressConfirm: () => {
              CommonModal?.hide();
              prepareDownload();
            },
            onPressClose: () => {
              CommonModal?.hide();
            },
          });
        }

        // 3. Check for internet connectivity
        const netState = await NetInfo.fetch();
        if (!netState.isConnected) {
          isEligible = false;
          // Show modal for network error
          CommonModal?.show({
            title: 'network_error_title',
            description: 'network_error_description',
            confirmButtonText: 'retry',
            closeButtonText: 'close',
            onPressConfirm: () => {
              CommonModal?.hide();
              prepareDownload();
            },
            onPressClose: () => {
              CommonModal?.hide();
            },
          });
        }

        // 4. (Optional) Entitlement check can be added here in the future
        // TODO: Implement entitlement check if required

        return isEligible;
      },
      [prepareDownload]
    );

    /**
     * Prepares content for download.
     * - Sets UI to pending state while preparing.
     * - Handles both movies and web series (fetches episode data if needed).
     * - Checks download eligibility (storage, URL, network).
     * - Initiates download if all checks pass.
     * - Handles errors gracefully by updating UI state.
     */
    const prepareDownload = useCallback(async () => {
      let finalContentData = null;

      // Set UI to pending state immediately for better user feedback
      setDownloadDetails({ downloadState: DOWNLOAD_STATES.PENDING });

      try {
        // Determine content type and fetch necessary data
        if (isWebSeries(seriesData?.contentType)) {
          // Attempt to fetch episode data for web series
          const episodeData = await getSeriesData(
            contentData,
            null,
            (error) => {
              // If fetching fails, set state to retry
              setDownloadDetails({ downloadState: DOWNLOAD_STATES.RETRY });
              console.log('🚀 ~ prepareDownload ~ error:', error);
            }
          );

          finalContentData = episodeData?.meta;
          finalContentData.seriesData = {
            coverImage: seriesData?.coverImage,
            defaultTitle: seriesData?.defaultTitle,
            isOpen: seriesData?.isOpen,
            episodeDetails: seriesData?.episodeDetails,
            seasonList: seriesData?.seasonList,
            yearOfRelease: seriesData?.yearOfRelease,
            genre: seriesData?.genre,
            ageRating: seriesData?.ageRating,
            format: seriesData?.format,
            title: seriesData?.title,
            description: seriesData?.description,
            actor: seriesData?.actor,
            directors: seriesData?.directors,
            audioInfo: seriesData?.audioInfo,
            subtitlesInfo: seriesData?.subtitlesInfo,
          };
        } else if (isMovie(contentData?.contentType)) {
          finalContentData = contentData;
        }

        // Check if the content is eligible for download (storage, URL, network)
        const isEligible = await checkDownloadEligibility(finalContentData);
        if (!isEligible) {
          setDownloadDetails({ downloadState: DOWNLOAD_STATES.DEFAULT });
          return;
        }

        // All checks passed, start the download
        startDownload(finalContentData);
      } catch (error) {
        // Catch any unexpected errors and set state to retry
        setDownloadDetails({ downloadState: DOWNLOAD_STATES.RETRY });
        // Optionally, log error for debugging
        // console.error('Error in prepareDownload:', error);
      }
    }, [
      seriesData?.contentType,
      seriesData?.coverImage,
      seriesData?.defaultTitle,
      seriesData?.isOpen,
      seriesData?.episodeDetails,
      seriesData?.seasonList,
      seriesData?.yearOfRelease,
      seriesData?.genre,
      seriesData?.ageRating,
      seriesData?.format,
      seriesData?.title,
      seriesData?.description,
      seriesData?.actor,
      seriesData?.directors,
      seriesData?.audioInfo,
      seriesData?.subtitlesInfo,
      contentData,
      checkDownloadEligibility,
      startDownload,
    ]);

    /**
     * Handle download button press based on current download status
     * Manages different actions for different download states
     */
    const actionOnPress = useCallback(() => {
      if (DOWNLOAD_STATES.isCompleted(downloadStatus)) {
        //No Action Required
        return;
      }
      if (
        DOWNLOAD_STATES.isCompleted(downloadStatus) ||
        DOWNLOAD_STATES.isError(downloadStatus) ||
        DOWNLOAD_STATES.isPending(downloadStatus) ||
        DOWNLOAD_STATES.isDownloading(downloadStatus)
      ) {
        // For existing downloads, show removal confirmation
        const removeDownload = () => {
          if (downloadId) {
            DownloadManager.removeDownload(downloadId, () => {
              resetDownloadStates();
              getDownloadDetails();
              onRemove?.(downloadId);
            });
          }
        };
        DownloadRemoveAlert({ removeDownload, translate });
        return;
      } else {
        // For new downloads, start preparation process
        prepareDownload();
      }
    }, [
      contentData,
      downloadStatus,
      downloadId,
      getDownloadDetails,
      resetDownloadStates,
      startDownload,
      onRemove,
      prepareDownload,
    ]);

    return (
      <>
        <TouchableOpacity
          onPress={actionOnPress}
          style={
            isCircleStyle ? styles.buttonContainer : styles.plainButtonContainer
          }
        >
          {renderDownloadIcon}
        </TouchableOpacity>
      </>
    );
  }
);

/**
 * Alert dialog for confirming download removal
 * Shows a modal asking user to confirm before removing a download
 *
 * @param {Object} props - Component props
 * @param {Function} props.removeDownload - Callback function to remove download
 */
const DownloadRemoveAlert = ({ removeDownload, translate }) => {
  CommonModal?.show({
    title: 'cancel_download',
    description: 'do_you_want_to_remove_this_download',
    confirmButtonText: 'cancel_download',
    closeButtonText: 'continue_download',
    onPressConfirm: () => {
      CommonModal?.hide();
      removeDownload();
      applyCustomToast(translate('cancel_download'), '', ToastType.SOOKA_TOAST);
    },
    onPressClose: () => {
      CommonModal?.hide();
    },
  });
  // Legacy Alert implementation kept for reference
  // Alert.alert('Download completed', 'Do you want to remove this download?', [
  //   {
  //     text: 'Cancel',
  //     style: 'cancel',
  //   },
  //   {
  //     text: 'Remove',
  //     onPress: removeDownload,
  //   },
  // ]);
};

export default DownloadStatusButton;

/**
 * Fetch series data for download
 * Retrieves episode-specific data needed for downloading series content
 *
 * @param {Object} contentData - Content data object with contentId
 * @param {Function} seriesCB - Callback for successful series data fetch
 * @param {Function} errorCB - Callback for error handling
 */
const getSeriesData = async (
  contentData,
  seriesCB = () => {},
  errorCB = () => {}
) => {
  try {
    const successCallBack = (response, seriesMetaData) => {
      seriesCB(response, seriesMetaData);
    };
    const errorCallBack = (error) => {
      console.log('🚀 ~ errorCallBack ~ error:', error);
      errorCB(error);
    };

    const body = {
      contentId: contentData?.contentId,
    };

    const getCurrentEpisodeData = await fetchEpisodeDataNoDispatch({ ...body });
    return getCurrentEpisodeData;
  } catch (error) {
    errorCB(error);
  }
};
