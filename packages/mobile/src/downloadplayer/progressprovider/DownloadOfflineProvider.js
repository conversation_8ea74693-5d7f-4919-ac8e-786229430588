import React, {
  useMemo,
  useEffect,
  useContext,
  createContext,
  useCallback,
} from 'react';
import CONSTANTS from '@video-ready/common/utils/constant';
import DownloadManager from '../utils/DownloadManager';
import useStateRef from '../../hooks/useStateRef';

const { DOWNLOAD_STATES } = CONSTANTS;

/**
 * Context for managing offline download state across the application
 * Provides download progress and status information to components
 */
const OfflineDownloadContext = createContext();

/**
 * Progress checkpoints to track download progress (0%, 25%, 50%, 75%, 100%)
 * Used to optimize state updates by only triggering at meaningful thresholds
 */
const PROGRESS_CHECKPOINTS = [0, 0.25, 0.5, 0.75, 1];

/**
 * Provider component that manages download state and events
 *
 * Handles download progress tracking, event listening, and provides
 * download state information to child components through context.
 *
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 * @returns {JSX.Element} Provider component
 */
const OfflineDownloadProvider = ({ children }) => {
  // State to track download progress and events with ref access for callbacks
  const [offlineDownloadState, setOffDownloadState, offlineDownloadStateRef] =
    useStateRef({
      event: '',
      data: {},
    });

  /**
   * Callback to handle download progress events from native module
   * Processes events and updates state based on download progress
   *
   * @param {Object} event - Download event from native module
   * @param {string} event.state - Current download state
   * @param {number} event.percentageComplete - Download progress percentage
   */
  const listenerCallback = useCallback((event) => {
    if (!event) {
      // Skip processing for empty events
      // return;
    }

    // Check if download is in progress
    const isDownloading = DOWNLOAD_STATES.isDownloading(event?.state);

    if (isDownloading) {
      // Calculate progress percentage (0-1 scale)
      const progress = Number(event?.percentageComplete) / 100;

      // Only update state when reaching a new progress checkpoint
      const currentProgress = offlineDownloadStateRef.current?.progress || 0;
      if (!offlineDownloadStateRef.current || progress > currentProgress) {
        const nextCheckpoint = PROGRESS_CHECKPOINTS.find(
          (cp) => cp > currentProgress
        );

        if (nextCheckpoint && progress >= nextCheckpoint) {
          setOffDownloadState({ ...event, progress });
        }
      }
    } else {
      // Update state for non-downloading events (completed, error, etc.)
      setOffDownloadState(event);
    }
  }, []);

  /**
   * Set up download listener on component mount
   * Registers callback with DownloadManager to receive download events
   */
  useEffect(() => {
    if (!DownloadManager) {
      return;
    }

    DownloadManager._setupDownloadListener(listenerCallback);

    // Cleanup listener on component unmount
    return () => {
      DownloadManager._unregisterDownloadListener();
    };
  }, [listenerCallback]);

  /**
   * Reset download state to initial values
   * Used when downloads are removed or need to be reset
   */
  const resetDownloadStates = useCallback(() => {
    setOffDownloadState(null);
  }, []);

  /**
   * Memoized context value to prevent unnecessary re-renders
   * Contains current download state and reset function
   */
  const providerValue = useMemo(
    () => ({
      offlineDownloadState,
      resetDownloadStates,
    }),
    [offlineDownloadState, resetDownloadStates]
  );

  return (
    <OfflineDownloadContext.Provider value={providerValue}>
      {children}
    </OfflineDownloadContext.Provider>
  );
};

export default OfflineDownloadProvider;

/**
 * Hook to access offline download context
 * Provides download state and management functions to components
 *
 * @returns {Object} Context containing offlineDownloadState and reset function
 * @throws {Error} If used outside of OfflineDownloadProvider
 */
export const useOfflineDownload = () => {
  const context = useContext(OfflineDownloadContext);
  if (!context) {
    throw new Error(
      'useOfflineDownload must be used within an OfflineDownloadProvider'
    );
  }
  return context;
};
