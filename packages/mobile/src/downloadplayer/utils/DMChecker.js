import { store } from '@video-ready/common/store/configureStore';
import CONSTANTS from '@video-ready/common/utils/constant';
import { Platform } from 'react-native';

/**
 * Utility for checking download eligibility of content
 * Provides methods to verify if content can be downloaded based on platform requirements
 * Ensures proper DRM support for each platform before allowing downloads
 */
export const DownloadManagerChecker = {
  /**
   * Checks if content is eligible for download based on platform-specific requirements
   * Verifies that appropriate DRM URLs and certificates are available for the platform
   *
   * @param {Array} playUrlDetail - Array of playback URLs and their details
   * @returns {boolean} - True if content can be downloaded, false otherwise
   */
  checkDownloadEligibility: (playUrlDetail) => {
    try {
      // Return false if no playback details are provided
      if (!playUrlDetail || !Array.isArray(playUrlDetail)) {
        console.log('Invalid or missing playUrlDetail');
        return false;
      }

      // Find the full asset URL from the playback details
      const playbackUrl = playUrlDetail.find((item) =>
        CONSTANTS.MediaURLAssetType.isFullAsset(item.contentType)
      );

      // Return false if no full asset URL is found
      if (!playbackUrl) {
        console.log('No full asset URL found in playback details');
        return false;
      }

      // For Android, check if widevine DRM URLs are available
      if (
        Platform.OS === 'android' &&
        playbackUrl?.dash?.widevine &&
        playbackUrl?.licenseAcquisitionUrl?.widevine
      ) {
        return true;
      }

      // For iOS, check if fairplay DRM URLs are available
      if (Platform.OS === 'ios') {
        const drmCred = store.getState().auth?.drmCertificateInfo;

        const hasRequiredDrmInfo =
          playbackUrl?.hls?.fairplay &&
          drmCred?.licenseServerFairplay &&
          drmCred?.certificateUrl;

        if (hasRequiredDrmInfo) {
          return true;
        }
        console.log('Missing required FairPlay DRM info for iOS download');
      }

      // If neither platform conditions are met, content cannot be downloaded
      return false;
    } catch (error) {
      console.error('Error checking download eligibility:', error);
      return false;
    }
  },
};
