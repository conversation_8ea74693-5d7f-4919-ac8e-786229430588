import {
  downloadContent,
  removeDownloadByName,
  listDownloadedContent,
  initDownloaderService,
  removeAllDownloads,
} from 'react-native-irdetodm';
import { AsyncStorageKey } from 'mobile/src/utils';
import { NativeModules, NativeEventEmitter, Platform } from 'react-native';
import LocalStorageService from '../../utils/LocalStorageService';
import { flatContentPlayUrls } from '../../screens/NewPlayer/helper';
import { store } from '@video-ready/common/store/configureStore';
import DeviceInfo from 'react-native-device-info';
import RNFS from 'react-native-fs';
import {
  fetchVTT,
  parseVTT,
} from '../../screens/NewPlayer/components/SeekSlider/utils';
import { convertUrl } from '../../screens/NewPlayer/components/SeekSlider';
// Create event emitter for native download events
const emitter = new NativeEventEmitter(NativeModules?.Irdetodm);

/**
 * Service class to manage content downloads
 * This class provides a singleton interface to handle all download-related operations
 * including adding, removing, listing and tracking downloads
 */
class DownloadManagerService {
  downloadListener = null;

  constructor() {
    this._initDownloader();
    // this._setupDownloadListener();
  }

  /**
   * Get singleton instance of download manager
   * Ensures only one instance of the download manager exists throughout the app
   * @returns {DownloadManagerService} Download manager instance
   */
  static getInstance() {
    if (!DownloadManagerService.instance) {
      DownloadManagerService.instance = new DownloadManagerService();
    }
    return DownloadManagerService.instance;
  }

  /**
   * Initialize the download service
   * Sets up the native download module with required configuration
   * @param {Function} cb - Optional callback after initialization
   */
  async _initDownloader(cb) {
    await initDownloaderService?.(1, () => {
      console.log('Downloader initialized');
      cb?.();
    });
  }

  /**
   * Remove all downloads from the device
   * Clears both the native download queue and local storage records
   */
  async removeAllDownloads() {
    removeAllDownloads();
    LocalStorageService.setItem(AsyncStorageKey.OFFLINE_DATA, {});
  }

  /**
   * Add downloaded content data to local storage
   * Stores metadata about downloaded content for later retrieval
   * @param {Object} contentData - Content metadata to store
   */
  async addItemToLocalStorage(contentData, downloadId, videoItem) {
    // Get cover image URL from either boxCoverImage or coverImage
    let coverImage = contentData?.boxCoverImage ?? contentData?.coverImage;
    // Get cover logo URL if available
    let coverLogo = contentData?.boxCoverLogo;
    let seriesCoverImage = contentData?.seriesData?.coverImage;

    // Download and store cover image locally if URL exists
    if (coverImage) {
      const localCoverImage = await downloadImage(coverImage);
      if (localCoverImage) {
        contentData.localCoverImage = localCoverImage;
      }
    }

    // Download and store series cover logo locally if URL exists
    if (seriesCoverImage) {
      const localCoverImage = await downloadImage(seriesCoverImage);
      if (localCoverImage) {
        contentData.seriesData.localCoverImage = localCoverImage;
      }
    }
    // Download and store cover logo locally if URL exists
    if (coverLogo) {
      const localCoverLogo = await downloadImage(coverLogo);
      if (localCoverLogo) {
        contentData.localCoverLogo = localCoverLogo;
      }
    }

    //download scrubbing vtt file
    // const scrubbingVttFile = await downloadScrubbingVttFile(
    //   videoItem?.url,
    //   downloadId
    // );

    // if (scrubbingVttFile) {
    //   contentData.scrubbingVttFile = scrubbingVttFile;
    // }
    //

    try {
      const allDownloads =
        (await LocalStorageService.getItem(AsyncStorageKey.OFFLINE_DATA)) || {};
      const profileId = getProfileId();

      // Initialize profile array if it doesn't exist
      if (!Array.isArray(allDownloads[profileId])) {
        allDownloads[profileId] = [];
      }

      // Check if content already exists for this profile
      const exists = allDownloads[profileId].some((item) => {
        return item.contentId === contentData.contentId;
      });

      if (!exists) {
        allDownloads[profileId].push(contentData);
        LocalStorageService.setItem(AsyncStorageKey.OFFLINE_DATA, allDownloads);
      }
    } catch (error) {
      console.error('Error adding item to local storage:', error);
      throw error;
    }
  }

  /**
   * Remove downloaded content data from local storage
   * Deletes metadata about a specific download
   * @param {string} downloadId - ID of download to remove
   */
  async removeItemFromLocalStorage(downloadId) {
    try {
      const allDownloads =
        (await LocalStorageService.getItem(AsyncStorageKey.OFFLINE_DATA)) || {};
      const profileId = getProfileId();

      if (allDownloads[profileId]) {
        // Filter out the content to be removed
        allDownloads[profileId] = allDownloads[profileId].filter((item) => {
          if (item.downloadId === downloadId) {
            deleteImage(item?.localCoverImage);
            deleteImage(item?.localCoverLogo);
            if (item?.seriesData?.localCoverImage) {
              deleteImage(item?.seriesData?.localCoverImage);
            }
            if (item?.scrubbingVttFile) {
              item?.scrubbingVttFile.forEach((tile) => {
                deleteImage(tile?.localPath);
              });
            }
            return false;
          }
          return true;
        });

        LocalStorageService.setItem(AsyncStorageKey.OFFLINE_DATA, allDownloads);
      }
    } catch (error) {
      console.error('Error removing item from local storage:', error);
      throw error;
    }
  }

  /**
   * Add content to download queue
   * Prepares content for download and initiates the download process
   * @param {Object} contentData - Content metadata
   * @param {string} downloadId - Unique download identifier
   * @param {Function} cb - Callback after queuing
   */
  addToDownloadQueue(contentData, downloadId, cb) {
    const videoItem = getDMVideoItem(contentData, downloadId);

    if (!videoItem) {
      console.log('No video item found');
      cb?.(false);
      return;
    }
    if (contentData && downloadId) {
      contentData.downloadId = downloadId;
    }
    // Save in Local while download start
    this.addItemToLocalStorage(contentData, downloadId, videoItem);

    downloadContent?.(
      videoItem,
      (response) => {
        console.log('🚀 ~ downloadContent response:', response);
      },
      (error) => {
        console.log('🚀 ~ downloadContent error:', error);
      }
    );
    cb?.();
  }

  /**
   * Remove a download
   * Deletes a download from both the native module and local storage
   * @param {string} downloadId - ID of download to remove
   * @param {Function} cb - Callback after removal
   */
  removeDownload(downloadId, cb) {
    removeDownloadByName(downloadId, (response) => {});
    this.removeItemFromLocalStorage(downloadId);
    cb?.();
  }

  /**
   * Set up listener for download events
   * Establishes a connection to native download events
   * @param {Function} cb - Callback for download events
   */
  _setupDownloadListener(cb) {
    if (this.downloadListener) return;

    this.downloadListener = emitter.addListener(
      'sendEventWithName',
      (event) => {
        console.log('Download Event Listener:', event);
        cb?.(event);
      }
    );
  }

  /**
   * Remove download event listener
   * Cleans up event listeners to prevent memory leaks
   */
  _unregisterDownloadListener() {
    if (this.downloadListener) {
      this.downloadListener?.remove?.();
      this.downloadListener = null;
    }
  }

  /**
   * Get all downloaded content
   * Retrieves the list of all downloads from the native module
   * @param {Function} getAllDownloadFromIdmCallBack - Callback with downloads list
   */
  getAllDownloads(getAllDownloadFromIdmCallBack = () => {}) {
    listDownloadedContent((data) => {
      getAllDownloadFromIdmCallBack(data);
    });
  }

  /**
   * Get downloads from local storage
   * Retrieves download metadata from AsyncStorage, optionally including native download data
   * @param {boolean} shouldAddPluginDownloadData - Whether to include native download data
   * @returns {Promise<Array>} List of downloads
   */
  async getDownloadsFromLocalStorage(shouldAddPluginDownloadData = false) {
    const profileId = getProfileId();
    const allDownloads =
      (await LocalStorageService.getItem(AsyncStorageKey.OFFLINE_DATA)) || {};

    // Only add dmData if 'shouldAddPluginDownloadData' variable is true
    let downloads = allDownloads?.[profileId] || [];

    if (
      typeof shouldAddPluginDownloadData !== 'undefined' &&
      shouldAddPluginDownloadData
    ) {
      const pluginDownloads = await new Promise((resolve) =>
        this.getAllDownloads(resolve)
      );

      downloads = downloads.map((download) => {
        const dmData = pluginDownloads.find(
          (pluginDownload) => pluginDownload.name === download.downloadId
        );
        return {
          ...download,
          dmData,
        };
      });
    }

    return downloads || [];
  }

  /**
   * Get specific download content from local storage
   * Retrieves metadata for a specific download, optionally including native download data
   * @param {string} downloadId - ID of download to retrieve
   * @param {boolean} wantPluginDownloadData - Whether to include native download data
   * @returns {Promise<Object>} Download item
   */
  async getDownloadContentFromLocalStorage(
    downloadId,
    wantPluginDownloadData = false
  ) {
    const allDownloads = await this.getDownloadsFromLocalStorage();
    let downloadItem = allDownloads.find(
      (download) => download?.downloadId === downloadId
    );
    //adding plugin download data in dmData key
    if (wantPluginDownloadData) {
      const DMDownloads = await new Promise((resolve) =>
        this.getAllDownloads(resolve)
      );
      const dmDownload = DMDownloads.find(
        (download) => download?.name === downloadId
      );

      downloadItem = {
        ...downloadItem,
        dmData: dmDownload,
      };
    }

    return downloadItem;
  }

  /**
   * Generate unique download ID for content
   * Creates a profile-specific identifier for downloaded content
   * @param {string} contentId - Content identifier
   * @returns {string} Unique download ID
   */
  getDownloadId(contentId) {
    const profileId = getProfileId();
    return `${profileId}-${contentId}`;
  }

  /**
   * Check if device has sufficient storage space for downloads
   * Verifies that enough free space is available before starting downloads
   * @param {number} requiredSpace - Space required in bytes (defaults to 2GB)
   * @returns {Promise<boolean>} True if sufficient space is available
   */
  async checkStorageSpace(requiredSpace = 2 * 1024 * 1024 * 1024) {
    if (__DEV__) {
      requiredSpace = 200 * 1024 * 1024; //200mb for DEV local
    }
    try {
      // Get free disk storage information
      const freeDiskStorage = await this._getFreeDiskStorage();
      // Check if available space is sufficient
      const hasEnoughSpace = freeDiskStorage > requiredSpace;

      return hasEnoughSpace;
    } catch (error) {
      console.error('Error checking storage space:', error);
      return false;
    }
  }

  /**
   * Get available free disk storage on device
   * Retrieves platform-specific storage information
   * @returns {Promise<number>} Free space in bytes
   * @private
   */
  async _getFreeDiskStorage() {
    try {
      if (Platform.OS === 'android') {
        // For Android, use RNFetchBlob or DeviceInfo
        const freeDiskStorage = await DeviceInfo.getFreeDiskStorage();

        return freeDiskStorage;
      } else if (Platform.OS === 'ios') {
        // For iOS, use RNFetchBlob or DeviceInfo
        const freeDiskStorage = await DeviceInfo.getFreeDiskStorage();
        return freeDiskStorage;
      }

      // Default fallback
      return 0;
    } catch (error) {
      console.error('Error getting free disk storage:', error);
      return 0;
    }
  }
}

export default DownloadManagerService.getInstance();

/**
 * Create video item configuration for download
 * Prepares the content metadata in the format required by the native download module
 * @param {Object} contentData - Content metadata containing playback URLs and DRM info
 * @param {string} downloadId - Unique identifier for the download item
 * @returns {Object|null} Video item config object formatted for native module, or null if invalid
 */
const getDMVideoItem = (contentData, downloadId) => {
  // Extract content URLs from the playback details
  const { contentUrls = {} } = flatContentPlayUrls(contentData?.playUrlDetail);
  // Return null if no playback URL is available
  if (!contentUrls?.playbackUrl) {
    console.log('No playbackUrl found');
    return null;
  }

  // Get DRM credentials from Redux store
  const drmCredentials = store.getState().auth?.drmCertificateInfo;

  // Set license URL based on platform - FairPlay for iOS, Widevine for Android
  const licenseUrl =
    Platform.OS === 'ios'
      ? drmCredentials?.licenseServerFairplay
      : contentUrls?.licenseServerWidevine;

  // Set certificate URL for iOS FairPlay DRM
  const certificateUrl =
    Platform.OS === 'ios' && drmCredentials?.certificateUrl
      ? drmCredentials?.certificateUrl
      : 'none';

  // Determine DRM protection type based on license availability and platform
  const protectionType = !licenseUrl
    ? 'none'
    : Platform.OS === 'ios'
      ? 'fairplay'
      : 'widevine';

  // Set content type based on platform - HLS for iOS, DASH for Android
  const contentType = Platform.OS === 'ios' ? 'HLS' : 'DASH';

  // Construct the video item configuration object
  const videoItem = {
    audioLanguage: '', // Default empty audio language
    mIsStartOver: true, // Allow restart of download
    subtitleLanguage: '', // Default empty subtitle language
    url: contentUrls?.playbackUrl, // Content playback URL
    bitrate: '720', // Default video quality
    title: downloadId, // Use download ID as title
    protectionType: protectionType, // DRM protection type
    certificateURL: certificateUrl, // DRM certificate URL
    licenseUrl: licenseUrl, // DRM license server URL
    contentType: contentType, // Content streaming format
  };

  console.log('🚀 ~ getDMVideoItem ~ videoItem:', videoItem);
  return videoItem;
};

/**
 * Get current profile ID from store
 * Retrieves the active user profile ID from Redux store
 * @returns {string} Profile ID
 */
const getProfileId = () => {
  const profileID =
    store.getState().auth.signInResponse?.data?.subscriber?.profileId;
  return profileID;
};

/**
 * Downloads an image from a URL and saves it locally
 * @param {string} imageUrl - The URL of the image to download
 * @returns {Promise<string|null>} - Promise resolving to local file path or null if download fails
 */
const downloadImage = async (imageUrl, newFilename) => {
  if (!imageUrl) {
    console.log('No image URL provided');
    return null;
  }

  try {
    // Create a unique filename based on the URL
    const filename =
      newFilename || imageUrl.split('/').pop() || `image_${Date.now()}.jpg`;
    const localPath = `${RNFS.DocumentDirectoryPath}/${filename}`;

    // Check if file already exists
    const exists = await RNFS.exists(localPath);
    if (exists) {
      console.log('Image already downloaded:', localPath);
      return localPath;
    }

    // Download the file
    const result = await RNFS.downloadFile({
      fromUrl: imageUrl,
      toFile: localPath,
    }).promise;

    if (result.statusCode === 200) {
      return localPath;
    } else {
      console.error('Failed to download image:', result);
      return null;
    }
  } catch (error) {
    console.error('Error downloading image:', error);
    return null;
  }
};

const deleteImage = async (imagePath) => {
  if (!imagePath) {
    console.log('No image path provided');
    return;
  }
  await RNFS.unlink(imagePath);
  console.log('Image deleted:', imagePath);
};

/**
 * Downloads and processes the video scrubbing VTT file and its associated thumbnail images
 * @param {string} playbackUrl - The URL of the video playback file
 * @param {string} downloadId - Unique identifier for the download
 * @returns {Promise<Array|undefined>} - Array of parsed tiles with local paths or undefined if download fails
 */
const downloadScrubbingVttFile = async (playbackUrl, downloadId) => {
  // Convert the playback URL to the VTT file URL
  const updatedVttUrl = convertUrl(playbackUrl);

  // Fetch the VTT file content
  const vttContent = await fetchVTT(updatedVttUrl);

  // Return early if no VTT content was found
  if (!vttContent) {
    console.log('No vtt content found');
    return;
  }

  // Parse the VTT file to extract thumbnail information
  const parsedLocalTiles = parseVTT(vttContent, updatedVttUrl);

  // Process tiles in batches to avoid overwhelming the device
  const batchSize = 10;
  for (let i = 0; i < parsedLocalTiles.length; i += batchSize) {
    // Create a batch of tiles to process
    const batch = parsedLocalTiles.slice(i, i + batchSize);

    // Download images in parallel with a single timestamp for the batch
    const batchTimestamp = Date.now();
    const downloadPromises = batch.map(async (tile, index) => {
      // Create a unique filename for each thumbnail
      const filename = `image_${downloadId}_${batchTimestamp}_${index}.jpg`;

      // Download the thumbnail image
      const localPath = await downloadImage(tile.fullUrl, filename);
      console.log('[downloadScrubbingVttFile]-', index, ' ', tile.timeRange);

      // Store the local path in the tile object if download was successful
      if (localPath) {
        tile.localPath = localPath;
      }
    });

    // Wait for the current batch to complete before moving to the next
    await Promise.all(downloadPromises);
  }

  return parsedLocalTiles;
};
