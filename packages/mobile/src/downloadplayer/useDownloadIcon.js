import React, { useMemo } from 'react';
import { normalize } from 'mobile/src/styles/Mixins';
import {
  DownloadDetail,
  Download25,
  Download50,
  Download75,
  DownloadError,
  Downloaded,
  DownloadedQueued,
} from '@video-ready/common/utils/imageConstants';
import CONSTANTS from '@video-ready/common/utils/constant';

const { DOWNLOAD_STATES } = CONSTANTS;

/**
 * Custom hook that returns the appropriate download icon based on download status and progress
 *
 * This hook determines which icon to display for different download states:
 * - Downloaded (completed)
 * - Error state
 * - Queued/Pending
 * - In progress (with different icons for progress ranges)
 * - Not downloaded (default state)
 *
 * @param {string} status - Current download status from DOWNLOAD_STATES
 * @param {number} progress - Download progress percentage (0-100)
 * @param {Array} deps - Additional dependencies for the useMemo hook
 * @returns {JSX.Element} The appropriate icon component with consistent sizing
 */
// Common props for all icons to ensure consistent sizing
const iconProps = {
  width: normalize(20),
  height: normalize(20),
};
const useDownloadIcon = (status, progress, deps = []) => {
  return useMemo(() => {
    const downloadProgress = parseInt(progress, 10);

    // Return the Downloaded icon when download is complete
    if (DOWNLOAD_STATES.isCompleted(status)) {
      return <Downloaded {...iconProps} />;
    }

    // Return the Error icon when download has failed
    if (DOWNLOAD_STATES.isError(status)) {
      return <DownloadError {...iconProps} />;
    }

    // Return the Queued icon when download is pending or just started
    if (
      DOWNLOAD_STATES.isPending(status) ||
      DOWNLOAD_STATES.isStarted(status)
    ) {
      return <DownloadedQueued {...iconProps} />;
    }

    // Return progress-specific icons based on download percentage
    if (DOWNLOAD_STATES.isDownloading(status) && downloadProgress != null) {
      if (downloadProgress <= 25) {
        return <DownloadedQueued {...iconProps} />;
      } else if (downloadProgress <= 50) {
        return <Download25 {...iconProps} />;
      } else if (downloadProgress <= 75) {
        return <Download50 {...iconProps} />;
      } else if (downloadProgress <= 100) {
        return <Download75 {...iconProps} />;
      }
    }

    // Default icon for content that is not being downloaded
    return <DownloadDetail {...iconProps} />;
  }, [progress, status, ...deps]);
};

export default useDownloadIcon;
