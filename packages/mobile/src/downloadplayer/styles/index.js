import { StyleSheet } from 'react-native';
import utils from '../../utils';
import {
  normalize,
  normalizeHeight,
  normalizeWidth,
} from '../../styles/Mixins';

export default StyleSheet.create({
  buttonContainer: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    paddingHorizontal: utils.isTab ? normalize(16) : 0,
    width: utils.isTab ? 'auto' : normalizeWidth(40),
    height: utils.isTab ? normalizeHeight(40) : normalizeWidth(40),
    borderRadius: normalize(50),
    marginHorizontal: utils.isTab ? normalizeWidth(8) : normalizeWidth(4),
    backgroundColor: '#00000080',
    borderWidth: normalizeWidth(1),
    borderColor: '#FFFFFF33',
    marginTop: normalizeWidth(2),
  },
  plainButtonContainer: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: normalizeWidth(2),
  },
});
