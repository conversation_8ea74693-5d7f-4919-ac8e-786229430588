import React from 'react';
import {
  View,
  TouchableOpacity,
  Text,
  FlatList,
  StatusBar,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { BackArrow } from '@video-ready/common/utils/imageConstants';
import AppGradient from 'mobile/src/components/AppBackground';
import styles from './styles';

const ProfilePreferenceList = ({
  title,
  data,
  selectedValue,
  onItemSelect,
  keyExtractor = (item) => item.id,
  renderItemContent,
  itemStyle,
}) => {
  const navigation = useNavigation();

  const onBackPress = () => {
    navigation.goBack();
  };

  const defaultRenderItemContent = (item) => (
    <Text style={styles.itemName}>{item.name}</Text>
  );

  const renderItem = ({ item }) => (
    <TouchableOpacity
      style={[styles.listItem, itemStyle]}
      onPress={() => onItemSelect(item)}
    >
      <View style={styles.itemContent}>
        {renderItemContent ? renderItemContent(item) : defaultRenderItemContent(item)}
      </View>
      {selectedValue === item.name && (
        <View style={styles.checkmark}>
          <Text style={styles.checkmarkIcon}>✓</Text>
        </View>
      )}
    </TouchableOpacity>
  );

  return (
    <>
      <StatusBar backgroundColor='#0000001A' barStyle='light-content' />
      <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
        <View style={styles.appGradient}>
          <AppGradient />
        </View>
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={onBackPress}>
            <BackArrow width={24} height={24} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>{title}</Text>
        </View>

        <FlatList
          data={data}
          renderItem={renderItem}
          keyExtractor={keyExtractor}
          showsVerticalScrollIndicator={false}
        />
      </SafeAreaView>
    </>
  );
};

export default ProfilePreferenceList;
