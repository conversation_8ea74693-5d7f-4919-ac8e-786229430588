import React from 'react';
import {
  View,
  TouchableOpacity,
  Text,
  FlatList,
  StatusBar,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { BackArrow } from '@video-ready/common/utils/imageConstants';
import AppGradient from 'mobile/src/components/AppBackground';
import ProfilePreferenceListItem from './ProfilePreferenceListItem';
import styles from './styles';

const ProfilePreferenceList = ({
  title,
  data,
  selectedValue,
  onItemSelect,
  keyExtractor = (item) => item.id,
  renderItemContent,
  itemStyle,
}) => {
  const navigation = useNavigation();

  const onBackPress = () => {
    navigation.goBack();
  };

  const renderItem = ({ item }) => (
    <ProfilePreferenceListItem
      item={item}
      selectedValue={selectedValue}
      onItemSelect={onItemSelect}
      renderItemContent={renderItemContent}
      itemStyle={itemStyle}
    />
  );

  return (
    <>
      <StatusBar backgroundColor='#0000001A' barStyle='light-content' />
      <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
        <View style={styles.appGradient}>
          <AppGradient />
        </View>
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={onBackPress}>
            <BackArrow width={24} height={24} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>{title}</Text>
        </View>

        <FlatList
          data={data}
          renderItem={renderItem}
          keyExtractor={keyExtractor}
          showsVerticalScrollIndicator={false}
        />
      </SafeAreaView>
    </>
  );
};

export default ProfilePreferenceList;
