import { StyleSheet } from 'react-native';
import { COLORS, FONTS } from '@video-ready/common/utils';
import {
  normalizeFont,
  normalizeHeight,
  normalizeWidth,
} from 'mobile/src/styles/Mixins';
import utils from 'mobile/src/utils/index';

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.COLOR_1F0C1ECC,
  },
  appGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: normalizeWidth(16),
    paddingVertical: normalizeHeight(16),
    minHeight: normalizeHeight(56),
    backgroundColor: '#0000001A',
  },
  backButton: {
    padding: normalizeWidth(4),
    marginRight: normalizeWidth(12),
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: utils.isTab ? normalizeFont(22) : normalizeFont(18),
    fontFamily: FONTS.MULISH_BOLD,
    color: COLORS.COLOR_EBEBEB,
    flex: 1,
    textAlign: 'left',
  },
  listItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: utils.isTab ? normalizeHeight(20) : normalizeHeight(16),
    paddingHorizontal: utils.isTab ? normalizeWidth(24) : normalizeWidth(16),
    marginBottom: normalizeHeight(12),
    minHeight: utils.isTab ? normalizeHeight(60) : normalizeHeight(50),
  },
  itemContent: {
    flex: 1,
  },
  itemName: {
    fontSize: utils.isTab ? normalizeFont(18) : normalizeFont(16),
    fontFamily: FONTS.MULISH_MEDIUM,
    color: COLORS.COLOR_EBEBEB,
    flex: 1,
  },
  itemDescription: {
    fontSize: utils.isTab ? normalizeFont(14) : normalizeFont(12),
    fontFamily: FONTS.MULISH_MEDIUM,
    color: COLORS.COLOR_EBEBEBB2,
    marginTop: normalizeHeight(4),
  },
  checkmark: {
    width: utils.isTab ? normalizeWidth(32) : normalizeWidth(24),
    height: utils.isTab ? normalizeHeight(32) : normalizeHeight(24),
    borderRadius: utils.isTab ? normalizeHeight(16) : normalizeHeight(12),
    backgroundColor: COLORS.COLOR_FFFFFF,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: normalizeWidth(16),
  },
  checkmarkIcon: {
    fontSize: utils.isTab ? normalizeFont(16) : normalizeFont(14),
    fontFamily: FONTS.MULISH_BOLD,
    color: COLORS.COLOR_000000,
  },
});
