import { StyleSheet } from 'react-native';

import { normalizeFont, normalizeHeight } from 'mobile/src/styles/Mixins';
import { COLORS, FONTS } from '@video-ready/common/utils';

const styles = StyleSheet.create({
  descriptionTextStyleEpisode: {
    paddingTop: normalizeHeight(6),
    color: COLORS.COLOR_FFFFFFB2,
    fontFamily: FONTS.MULISH_REGULAR,
    fontSize: normalizeFont(12),
    lineHeight: normalizeHeight(17),
  },
  keyValueContainer: {
    marginTop: normalizeHeight(6),
  },
  keyValuePairView: {
    flexDirection: 'row',
    marginBottom: normalizeHeight(4),
  },
});

export default styles;
