import { View, Text } from 'react-native';
import React, { useMemo } from 'react';
import appConfig from '@video-ready/common/utils/config';
import useTranslate from 'mobile/src//hooks/useTranslate';
import { KeyValuePair } from 'mobile/src/screens/ContentPage/components/ContentDescription';
import { parseListToString } from 'mobile/src/screens/ContentPage/helper';
import styles from './styles';

const CardSynopsis = ({
  text,
  confgStyle,
  style = {},
  loadMore,
  contentData,
}) => {
  const { getText } = useTranslate();

  //SubTitle for description meta data update.
  const subtitleLanguage = useMemo(() => {
    const subtitleLanguageMap = appConfig.getSubtitleLanguageMap();
    const subTitles = contentData?.subtitlesInfo;
    let list = [];
    if (!subTitles) {
      return list;
    } else if (Array?.isArray(subTitles)) {
      list = subTitles.map((item) => {
        return subtitleLanguageMap?.[item?.language] ?? item?.language;
      });
    } else {
      list.push(subtitleLanguageMap[subTitles]);
    }
    return list;
  }, [contentData?.subtitlesInfo]);

  const audioLanguages = useMemo(() => {
    const audioLanguageMap = appConfig.getSubtitleLanguageMap();
    const audio = contentData?.audioInfo;

    if (!audio) {
      return [];
    }

    return Array.isArray(audio)
      ? audio.map(
          (item) => audioLanguageMap?.[item?.language] ?? item?.language
        )
      : [audioLanguageMap[audio] ?? audio];
  }, [contentData?.audioInfo]);

  const renderSubTitleLanguage = useMemo(() => {
    if (subtitleLanguage) {
      return (
        <KeyValuePair
          numberOfLines={1}
          value={parseListToString(subtitleLanguage)}
          key={getText('subtitles')}
          title={getText('subtitles')}
        />
      );
    } else {
      return null;
    }
  }, [getText, subtitleLanguage]);

  const renderAudioLanguage = useMemo(() => {
    if (audioLanguages) {
      return (
        <KeyValuePair
          numberOfLines={1}
          value={parseListToString(audioLanguages)}
          key={getText('audio')}
          title={getText('audio')}
        />
      );
    } else {
      return null;
    }
  }, [getText, audioLanguages]);

  return (
    <View style={confgStyle}>
      {loadMore && (
        <Text style={[styles.descriptionTextStyleEpisode, style]}>{text}</Text>
      )}
      {loadMore && (
        <View style={styles.keyValueContainer}>
          {contentData?.actor && contentData?.actor.length > 0 && (
            <KeyValuePair
              numberOfLines={1}
              value={contentData?.actor}
              key={getText('cast')}
              title={getText('cast')}
            />
          )}
          {contentData?.directors && (
            <KeyValuePair
              numberOfLines={1}
              value={contentData?.directors}
              key={getText('director')}
              title={getText('director')}
            />
          )}
          {renderSubTitleLanguage}
          {renderAudioLanguage}
        </View>
      )}
    </View>
  );
};

export default CardSynopsis;
