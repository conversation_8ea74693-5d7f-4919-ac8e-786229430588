import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { LeftArrow } from '@video-ready/common/utils/imageConstants';
import { normalizeHeight, normalizeWidth } from 'mobile/src/styles/Mixins';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import useTranslate from 'mobile/src/hooks/useTranslate';
import { useSafeHeights } from 'mobile/src/hooks/useSafeHeights';
import styles from './styles';

const MobileSuccessHeader = () => {
  const navigation = useNavigation();
  const handleCacel = () => {
    navigation.goBack();
  };

  const { getText } = useTranslate();
  const { safeEdges } = useSafeHeights();

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.headerContainer}>
        <TouchableOpacity onPress={handleCacel} style={styles.backArrow}>
          <LeftArrow width={normalizeWidth(24)} height={normalizeHeight(24)} />
        </TouchableOpacity>
        <Text numberOfLines={1} style={styles.headerText}>
          {getText('tv_pair')}{' '}
        </Text>
      </View>
      <View style={styles.statusBar(safeEdges.top)} />
    </SafeAreaView>
  );
};

export default MobileSuccessHeader;
