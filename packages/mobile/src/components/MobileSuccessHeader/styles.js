import { StyleSheet } from 'react-native';
import {
  normalizeFont,
  normalizeWidth,
  normalizeHeight,
} from 'mobile/src/styles/Mixins';
import { FONTS, COLORS } from '@video-ready/common/utils';
import { HEADER_HEIGHT_TAB } from 'mobile/src/utils/homeScreenMethods';

export default StyleSheet.create({
  container: {
    flex: 1,
  },
  mainContainer: {
    flexDirection: 'row',
    backgroundColor: COLORS.COLOR_0000001A,
    alignItems: 'center',
    height: normalizeHeight(40),
  },
  headerContainer: {
    flexDirection: 'row',
    backgroundColor: COLORS.COLOR_0000001A,
    alignItems: 'center',
    height: normalizeHeight(32),
  },
  backArrow: {
    marginRight: normalizeWidth(8),
    marginLeft: normalizeWidth(12),
  },
  headerText: {
    fontFamily: FONTS.MULISH_BOLD,
    fontSize: normalizeFont(16),
    color: COLORS.COLOR_EBEBEB,
  },
  statusBar: (statusBar) => ({
    top: 0,
    left: 0,
    right: 0,
    height: statusBar,
    position: 'absolute',
    backgroundColor: COLORS.COLOR_0000001A,
    zIndex: 1,
  }),
});
