import React from 'react';
import VerticalListView from './VerticalListView';

/**
 * MemoizedVerticalListView is a wrapper around VerticalListView
 * with custom memoization to avoid unnecessary re-renders.
 */
const MemoizedVerticalListView = React.memo(
  ({
    flatListRef,
    scrollHandler,
    onRefresh,
    refreshing,
    contentContainerStyle,
    data,
    renderItem,
    ListEmptyComponent,
    ListFooterComponent,
    showsVerticalScrollIndicator,
    keyExtractor,
    extraData,
    onEndReachedThreshold,
    onEndReached,
    estimatedItemSize,
    estimatedListSize,
    getItemLayout,
    initialNumToRender,
    getItemType,
  }) => (
    <VerticalListView
      flatListRef={flatListRef}
      scrollHandler={scrollHandler}
      onRefresh={onRefresh}
      refreshing={refreshing}
      contentContainerStyle={contentContainerStyle}
      data={data}
      renderItem={renderItem}
      ListEmptyComponent={ListEmptyComponent}
      ListFooterComponent={ListFooterComponent}
      showsVerticalScrollIndicator={showsVerticalScrollIndicator}
      keyExtractor={keyExtractor}
      extraData={extraData}
      onEndReachedThreshold={onEndReachedThreshold}
      onEndReached={onEndReached}
      estimatedItemSize={estimatedItemSize}
      estimatedListSize={estimatedListSize}
      getItemLayout={getItemLayout}
      initialNumToRender={initialNumToRender}
      getItemType={getItemType}
    />
  ),
  (prevProps, nextProps) => {
    return (
      prevProps.data === nextProps.data &&
      prevProps.refreshing === nextProps.refreshing &&
      prevProps.extraData === nextProps.extraData &&
      prevProps.ListFooterComponent === nextProps.ListFooterComponent
    );
  }
);

export default MemoizedVerticalListView;
