import { StyleSheet } from 'react-native';
import {
  normalizeFont,
  normalizeWidth,
  normalizeHeight,
} from 'mobile/src/styles/Mixins';
import { FONTS, COLORS } from '@video-ready/common/utils';

export default StyleSheet.create({
  mainContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    height: normalizeHeight(32),
  },
  backArrow: {
    paddingHorizontal: normalizeWidth(8),
  },
  textContainer: {
    flex: 1,
  },
  headerText: {
    fontFamily: FONTS.MULISH_BOLD,
    fontSize: normalizeFont(16),
    color: COLORS.COLOR_EBEBEB,
    marginRight: normalizeWidth(8),
  },
  statusBar: (statusBar) => ({
    top: 0,
    left: 0,
    right: 0,
    height: statusBar,
    position: 'absolute',
    backgroundColor: COLORS.COLOR_0000001A,
    zIndex: 1,
  }),
});
