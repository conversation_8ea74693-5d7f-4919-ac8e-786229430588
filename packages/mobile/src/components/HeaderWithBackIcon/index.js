import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { LeftArrow } from '@video-ready/common/utils/imageConstants';
import { normalizeHeight, normalizeWidth } from 'mobile/src/styles/Mixins';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useSafeHeights } from 'mobile/src/hooks/useSafeHeights';
import styles from './styles';

const HeaderWithBackIcon = ({ title, onBackPress }) => {
  const { safeEdges } = useSafeHeights();

  return (
    <SafeAreaView>
      <View style={styles.mainContainer}>
        <TouchableOpacity onPress={onBackPress} style={styles.backArrow}>
          <LeftArrow width={normalizeWidth(24)} height={normalizeHeight(24)} />
        </TouchableOpacity>
        <View style={styles.textContainer}>
          <Text numberOfLines={1} style={styles.headerText}>
            {title}
          </Text>
        </View>
      </View>
      <View style={styles.statusBar(safeEdges.top)} />
    </SafeAreaView>
  );
};

export default HeaderWithBackIcon;
