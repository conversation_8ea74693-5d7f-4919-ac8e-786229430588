import { useCallback, useState } from 'react';
import { runOnJS, useDerivedValue } from 'react-native-reanimated';

export function useSharedValueToState(sharedValue, formatFn) {
  const [state, setState] = useState(sharedValue.value);

  const setStateValue = useCallback(
    (val) => {
      try {
        if (typeof formatFn === 'function') {
          val = formatFn?.(val);
        }
      } catch (error) {}
      setState(val);
    },
    [formatFn]
  );
  useDerivedValue(() => {
    // Update state only if the value changes
    runOnJS(setStateValue)(sharedValue.value);
  }, [sharedValue]);

  return state;
}
