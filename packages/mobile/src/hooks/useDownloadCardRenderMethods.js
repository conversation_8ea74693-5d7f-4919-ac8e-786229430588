import { useCallback } from 'react';
import { toHHMMSS } from 'mobile/src/utils/commonMethods';
import { parseListToString } from 'mobile/src/screens/ContentPage/helper';
import useTranslate from 'mobile/src/hooks/useTranslate';
/**
 *
 * @returns this hooks is used for handling for Download card(Movies/Series/Episode) tags items.
 */
const useDownloadCardRenderMethods = () => {
  const { getText } = useTranslate();
  /**
   * in case of content = IsWebSeries, show the genreList ->item?.genreList?.secondary[0]?.genre
   * else (movie)  list -> item?.genreList
   */
  const genreList = useCallback((item) => {
    if (item?.genreList?.secondary && item?.genreList.secondary[0]?.genre) {
      return parseListToString(item?.genreList?.secondary[0]?.genre);
    } else {
      return parseListToString(item?.genreList);
    }
  }, []);

  // Download Screen (Screen-1) tags
  const webSeriesTags = useCallback(
    (item, tagList) => {
      const seriesData = item?.seriesData;
      if (seriesData?.isOpen) {
        if (seriesData?.episodeDetails?.episodeCount === 1) {
          tagList.push({
            text: `${seriesData?.episodeDetails?.episodeCount} ${getText(
              'episode'
            )}`,
          });
        } else if (seriesData?.episodeDetails?.episodeCount > 0) {
          tagList.push({
            text: `${seriesData?.episodeDetails?.episodeCount} ${getText(
              'episodes'
            )}`,
          });
        }
      } else {
        if (seriesData?.seasonList?.length === 1) {
          tagList.push({
            text: `${seriesData?.seasonList?.length} ${getText('season')}`,
          });
        } else if (seriesData?.seasonList?.length > 0) {
          tagList.push({
            text: `${seriesData?.seasonList?.length} ${getText('seasons')}`,
          });
        }
      }
      if (seriesData?.yearOfRelease > 0) {
        tagList.push({ text: seriesData?.yearOfRelease });
      }
      if (seriesData?.genre) {
        tagList.push({ text: seriesData?.genre });
      }
      if (seriesData?.ageRating) {
        tagList.push({ text: seriesData?.ageRating, textStyleBold: true });
      }
      if (seriesData?.format) {
        tagList.push({ text: seriesData?.format, textStyleBold: true });
      }
    },
    [getText]
  );
  /**
   * handling tags for Movies(Screen-1) & episode(Screen-2) card
   */
  const episodeMoviesItemTags = useCallback(
    (item, tagList, fromEpisodePage) => {
      if (item?.seasonNumber || item?.episodeNumber) {
        const season =
          item?.seasonNumber != null ? `S${item.seasonNumber}` : '';
        const episode =
          item?.episodeNumber != null ? `E${item.episodeNumber}` : '';

        tagList.push({ text: `${season} ${episode}` });
      }
      if (item?.episodeCount) {
        tagList.push({ text: `${item?.episodeCount} ${getText('episodes')}` });
      }
      const playableDuration =
        item?.playUrlDetail &&
        item?.playUrlDetail?.length > 0 &&
        item?.playUrlDetail[0].duration;

      if (item?.duration || playableDuration) {
        tagList.push({
          text: item?.duration ?? toHHMMSS(playableDuration, true),
        });
      }
      // show only for DownloadPage
      if (!fromEpisodePage) {
        if (item?.yearOfRelease > 0) {
          tagList.push({ text: item?.yearOfRelease });
        }

        if (item?.genreList) {
          tagList.push({ text: genreList(item) });
        }
      }

      if (item?.rating || item?.ageRating) {
        tagList.push({
          text: item?.rating ?? item?.ageRating,
          textStyleBold: true,
        });
      }
      if (item?.maxResolution > 0) {
        tagList.push({ text: item?.maxResolution });
      }
      if (item?.format) {
        tagList.push({ text: item?.format, textStyleBold: true });
      }
    },
    [genreList, getText]
  );

  return { webSeriesTags, episodeMoviesItemTags };
};
export default useDownloadCardRenderMethods;
