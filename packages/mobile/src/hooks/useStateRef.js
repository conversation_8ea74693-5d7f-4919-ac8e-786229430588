import { useRef, useState, useCallback } from 'react';

/**
 * Custom hook that combines useState and useRef to maintain both state and a reference
 * @param {any} initialValue - Initial value for the state
 * @returns {[any, Function]} Tuple containing current value and setter function
 */
const useStateRef = (initialValue) => {
  const [state, setState] = useState(initialValue);
  const stateRef = useRef(state);

  const setStateRef = useCallback((value) => {
    stateRef.current = value;
    setState(value);
  }, []);

  return [state, setStateRef, stateRef];
};

export default useStateRef;
