// pkceHelper.js
import CryptoJS from 'crypto-js';

// 🔐 Generate code_verifier using random string + timestamp
export const generateCodeVerifier = (length = 64) => {
  const charset =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~';
  return Array.from(
    { length },
    () => charset[Math.floor(Math.random() * charset.length)]
  ).join('');
};

// 🔐 Generate SHA256 code_challenge from code_verifier
export const generateCodeChallenge = async (codeVerifier) => {
  try {
    // Use crypto-js for SHA-256 hashing
    const hash = CryptoJS.SHA256(codeVerifier);
    const base64 = CryptoJS.enc.Base64.stringify(hash);
    return base64.replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
  } catch (error) {
    console.error('Error generating code challenge:', error);
    throw error;
  }
};

// Generate a random hex string of specified length
const generateRandomHexString = (length = 32) => {
  const hexChars = '0123456789abcdef';
  return Array.from(
    { length },
    () => hexChars[Math.floor(Math.random() * hexChars.length)]
  ).join('');
};

// 📦 Generate login params for /v1/auth/login
export const getLoginParams = async ({
  redirectUri = 'https://web.vrgo.test.xp.irdeto.com/home/<USER>',
} = {}) => {
  const state = generateRandomHexString(32);
  const codeVerifier = await generateCodeVerifier();
  const codeChallengeMethod = 'S256';
  const responseCode = 'code';

  const codeChallenge = await generateCodeChallenge(codeVerifier);

  const loginParamsString = `code_challenge=${codeChallenge}&code_challenge_method=${codeChallengeMethod}&redirect_uri=${encodeURIComponent(
    redirectUri
  )}&response_type=${responseCode}&state=${state}`;

  return {
    loginParamsString,
    codeVerifier,
  };
};
