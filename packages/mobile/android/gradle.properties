# Project-wide Gradle settings.

# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.

# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html

# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
# Default value: -Xmx512m -XX:MaxMetaspaceSize=256m
org.gradle.jvmargs=-Xmx4096m -XX:MaxMetaspaceSize=1024m

# When configured, <PERSON>rad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true

# AndroidX package structure to make it clearer which packages are bundled with the
# Android operating system, and which are packaged with your app's APK
# https://developer.android.com/topic/libraries/support-library/androidx-rn
android.useAndroidX=true
# Automatically convert third-party libraries to use AndroidX
android.enableJetifier=true
android.enableR8.fullMode=true
# Version of flipper SDK to use with React Native
FLIPPER_VERSION=0.125.0

# Use this property to specify which architecture you want to build.
# You can also override it from the CLI using
# ./gradlew <task> -PreactNativeArchitectures=x86_64
reactNativeArchitectures=armeabi-v7a,arm64-v8a,x86,x86_64

# Use this property to enable support to the new architecture.
# This will allow you to use TurboModules and the Fabric render in
# your application. You should enable this flag either if you want
# to write custom TurboModules/Fabric components OR use libraries that
# are providing them.
newArchEnabled=false
#org.gradle.java.home=/Applications/Android Studio.app/Contents/jbr/Contents/Home
# Keystore
PROD_STORE_FILE_NAME=astrogo.jks
MYAPP_UPLOAD_KEY_ALIAS=Astrogo
MYAPP_UPLOAD_STORE_PASSWORD=Astrogo@123
MYAPP_UPLOAD_KEY_PASSWORD=Astrogo@123
STAGING_UPLOAD_KEY_ALIAS=Astrogo
STAGING_UPLOAD_STORE_PASSWORD=Astrogo@123
STAGING_UPLOAD_KEY_PASSWORD=Astrogo@123
STORE_FILE_NAME_STAGING=astrogo.jks
SHA1=51:25:88:EA:8E:42:CA:77:E2:B5:9C:FA:C4:42:D1:80:11:03:9B:FB
SHA2=E0:D3:EF:90:D7:27:B7:DA:14:76:FB:7A:12:E8:11:90:EC:7A:11:51:2E:BA:EB:91:4B:7E:73:54:BD:32:C5:CB

# For conviva issue - https://pulse.conviva.com/learning-center/content/sensor_developer_center/sensor_integration/android/android_stream_sensor.htm
# Comment it if not using conviva video analytics.
android.enableDexingArtifactTransform=false