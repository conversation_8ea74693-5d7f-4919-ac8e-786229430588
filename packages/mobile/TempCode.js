import {
  View,
  Text,
  Button,
  ToastAndroid,
  NativeModules,
  NativeEventEmitter,
  ScrollView,
  StyleSheet,
} from 'react-native';
import React, { useEffect } from 'react';

import {
  downloadContent,
  pauseDownloadByName,
  resumeDownloadByName,
  removeDownloadByName,
  removeAllDownloads,
  initDownloaderService,
  listDownloadedContent,
} from 'react-native-irdetodm';
import { Platform } from 'react-native';
import Video, { DRMType } from 'react-native-video';
import utils from './src/utils';
export default function TempCode() {
  const [downloadStatus, setDownloadStatus] = React.useState(null);
  //list
  const [downloadList, setDownloadList] = React.useState([]);
  const source = `https://cdn-paytvc-test.bpk.dev.xp.irdeto.com/5696cbdb19b2f869b6e21dd845dda8fc-b4c836ba6757f095a866-20/5696cbdb19b2f869b6e21dd845dda8fc-b4c836ba6757f095a866-20/index.mpd`;
  const drm = {
    certificateUrl:
      'https://dub-tctr.test.ott.irdeto.com/licenseServer/streaming/v1/irdetoexperience/getcertificate?applicationId=irdetoexperience',
    headers: {},
    licenseServer:
      'https://irdetoexperience.test.ott.irdeto.com/licenseServer/widevine/v1/irdetoexperience/license?contentId=5696cbdb19b2f869b6e21dd845dda8fc-b4c836ba6757f095a866-20',
    type: 'widevine',
  };
  // const source = `https://vod-cld.bpk.dev.xp.irdeto.com/39048cad85ff602d43cf4e24e37859b6-f2a06c38dea52f985813-1/39048cad85ff602d43cf4e24e37859b6-f2a06c38dea52f985813-1/index.mpd`;
  // const drm = {
  //   certificateUrl:
  //     'https://dub-tctr.test.ott.irdeto.com/licenseServer/streaming/v1/irdetoexperience/getcertificate?applicationId=irdetoexperience',
  //   headers: {},
  //   licenseServer:
  //     'https://irdetoexperience.test.ott.irdeto.com/licenseServer/widevine/v1/irdetoexperience/license?contentId=39048cad85ff602d43cf4e24e37859b6-f2a06c38dea52f985813-1',
  //   type: 'widevine',
  // };
  // const source = `https://vod.bpk.dev.xp.irdeto.com/bpk-vod/vodc/default/e00cf83e7e0094bc1bba8b28e0ec3517-e68e70ff2605197c052e-1/e00cf83e7e0094bc1bba8b28e0ec3517-e68e70ff2605197c052e-1/index.m3u8`;
  // const drm = {
  //   certificateUrl:
  //     'https://irdetoexperience.test.ott.irdeto.com/licenseServer/streaming/v1/irdetoexperience/getcertificate?applicationId=irdetoexperience',
  //   headers: {
  //     Accept: 'application/base64',
  //     'Content-Type': 'application/x-www-form-urlencoded',
  //   },
  //   licenseServer: `https://irdetoexperience.test.ott.irdeto.com/licenseServer/streaming/v1/irdetoexperience/getckc?contentId=dev_hdm_fps&keyId=33a30486-e8de-4cf4-98c3-f53eaac0b1a2`,
  //   // licenseServer:
  //   //   'https://irexp-preint.test.ott.irdeto.com/licenseServer/widevine/v1/irexp-preint/license?contentId=e00cf83e7e0094bc1bba8b28e0ec3517-e68e70ff2605197c052e-1',
  //   type: 'fairplay',
  // };
  const downloadVideo = () => {
    const videoItem = {
      audioLanguage: '',
      mIsStartOver: true,
      subtitleLanguage: '',
      url: source,
      bitrate: '720',
      title: 'DownloadOneItem2',
      protectionType: Platform.OS == 'ios' ? 'fairplay' : 'widevine',
      certificateURL: Platform.OS == 'ios' ? 'none' : drm.certificateUrl,
      licenseUrl: Platform.OS == 'ios' ? 'none' : drm.licenseServer,
      contentType: Platform.OS !== 'ios' ? 'DASH' : 'HLS',
      // protectionType: Platform.OS === 'ios' ? 'fairplay' : 'widevine',
      // certificateURL: Platform.OS === 'ios' ? drm.certificateUrl : 'none',
      // licenseUrl: Platform.OS === 'ios' ? drm.licenseServer : drm.licenseServer,

      // ...(downloadObj?.drmCredentials && {
      //   token: downloadObj?.drmCredentials?.authXmlToken,
      // }),
    };
    console.log('🚀 ~ downloadVideo ~ videoItem:', videoItem);
    //  https://vod-cld.bpk.dev.xp.irdeto.com/39048cad85ff602d43cf4e24e37859b6-f2a06c38dea52f985813-1/39048cad85ff602d43cf4e24e37859b6-f2a06c38dea52f985813-1/index.mpd

    const onSuccess = (data) => {
      console.log('Download success', data);
      // ToastAndroid.show(
      //   'Download success',
      //   ToastAndroid.SHORT,
      //   ToastAndroid.CENTER
      // );
    };
    const onError = (error) => {
      console.log('Download error', error);
      // ToastAndroid.show(
      //   'Download error',
      //   ToastAndroid.SHORT,
      //   ToastAndroid.CENTER
      // );
    };
    downloadContent?.(videoItem, onSuccess, onError);
    // ToastAndroid.show(
    //   'Download video',
    //   ToastAndroid.SHORT,
    //   ToastAndroid.CENTER
    // );
    console.log('Download video');
  };
  useEffect(() => {
    let downloadListener;
    const callback = (response) => {
      setTimeout(() => {
        const emitter = new NativeEventEmitter(NativeModules?.Irdetodm);
        downloadListener = emitter?.addListener?.(
          'sendEventWithName',
          (event) => {
            // alert('receive');
            console.log('Download event', event);
            setDownloadStatus(event);
          }
        );
        listDownloadedContent?.((list) => {
          console.log('🚀 ~ setTimeout ~ list:', list);
          setDownloadList(list);
        });
      }, 2000);
      console.log(' 🚀 ~ initDownloaderService ', response);
      // updateDownloads?.();
    };
    // Initialize download service (1 denotes number of parallel downloads allowed)
    initDownloaderService?.(1, callback);

    return () => {
      if (downloadListener) {
        downloadListener?.remove?.();
      }
    };
  }, []);

  return true ? null : (
    <View
      style={{
        // flex: 1,
        height: 300,
        // height: 100,
        backgroundColor: 'white',
        // ...StyleSheet.absoluteFill,
        position: 'absolute',
        // top: 0,
        bottom: 0,
        left: 0,
        right: 0,
        justifyContent: 'center',
        // alignItems: 'center',
      }}
    >
      <Text>Download</Text>
      <ScrollView>
        <Button
          title={'Get Downloads'}
          onPress={() => {
            listDownloadedContent?.((list) => {
              console.log('🚀 ~ setTimeout ~ list:', list);
              setDownloadList(list);
            });
          }}
        />
        <Button title={'Download'} onPress={downloadVideo} />
        <Button
          title={'Resume'}
          onPress={() => resumeDownloadByName('DownloadOneItem2')}
        />
        <Button
          title={'Remove ALL'}
          onPress={() => {
            console.log('removeAllDownloads', removeAllDownloads);
            removeAllDownloads?.();
          }}
        />
        <Text>Download Status: {JSON.stringify(downloadStatus)}</Text>

        {/* <ScrollView contentContainerStyle={{ height: 100 }}> */}
        <Text>Download List: {JSON.stringify(downloadList)}</Text>
        {/* </ScrollView> */}
        {downloadList?.length > 0 && (
          <VideoView list={downloadList} index={0} />
        )}
      </ScrollView>
    </View>
  );
}

const VideoView = ({ list = [], index = 0 }) => {
  // const list = [
  //   {
  //     downloadState: 'COMPLETED',
  //     drmType: 'Widevine',
  //     forceL3: true,
  //     keySetId: 'a3NpZEVCMjkwQ0NF',
  //     localName: 'DownloadOneItem',
  //     localUrl:
  //       '/data/user/0/com.astrogo/files/DownloadOneItem/DownloadOneItem.mpd',
  //     mLicenseAcquisitionURL:
  //       'https://irdetoexperience.test.ott.irdeto.com/licenseServer/widevine/v1/irdetoexperience/license?contentId=39048cad85ff602d43cf4e24e37859b6-f2a06c38dea52f985813-1',
  //     mType: 'DASH',
  //     mURL: 'https://vod-cld.bpk.dev.xp.irdeto.com/39048cad85ff602d43cf4e24e37859b6-f2a06c38dea52f985813-1/39048cad85ff602d43cf4e24e37859b6-f2a06c38dea52f985813-1/index.mpd',
  //     name: 'DownloadOneItem',
  //     percentComplete: '1.0',
  //   },
  // ];
  let sourceUril = list[index]?.localUrl;
  const drm = {
    offlineDrm: list[index]?.keySetId,
    type: utils?.isIOS ? DRMType.FAIRPLAY : DRMType.WIDEVINE,
    headers: {
      // Authorization: tokens?.drmToken ? 'Bearer ' + tokens?.drmToken : '', // Did it because if not passing Authorization key it's taking value from source header in android
      // ...(tokens?.drmToken && {
      //   Authorization: 'Bearer ' + tokens?.drmToken,
      // }), //reverting above changes since drmToken is coming true if the content is not drm enabled
      ...(utils?.isIOS && {
        'Content-Type': 'application/x-www-form-urlencoded',
        Accept: 'application/base64',
      }),
    },
    // certificateUrl: list[0]?.,
    licenseServer: list[index]?.mLicenseAcquisitionURL,
    // offlineDrm: Platform.OS == 'ios' ? null : list[index]?.keySetId,
  };
  console.log(drm);

  // return (
  //   <Video
  //     source={{
  //       uri: `https://vod.bpk.dev.xp.irdeto.com/bpk-vod/vodc/default/e00cf83e7e0094bc1bba8b28e0ec3517-e68e70ff2605197c052e-1/e00cf83e7e0094bc1bba8b28e0ec3517-e68e70ff2605197c052e-1/index.m3u8`,
  //       type: Platform.OS == 'ios' ? 'm3u8' : 'mpd',
  //     }}
  //     drm={{
  //       certificateUrl:
  //         'https://irdetoexperience.test.ott.irdeto.com/licenseServer/streaming/v1/irdetoexperience/getcertificate?applicationId=irdetoexperience',
  //       // headers: {
  //       //   Accept: 'application/base64',
  //       //   'Content-Type': 'application/x-www-form-urlencoded',
  //       // },
  //       licenseServer:
  //         'https://irexp-preint.test.ott.irdeto.com/licenseServer/widevine/v1/irexp-preint/license?contentId=e00cf83e7e0094bc1bba8b28e0ec3517-e68e70ff2605197c052e-1',
  //       type: 'fairplay',
  //     }}
  //     style={{ width: 300, height: 300 }}
  //     controls={true}
  //     resizeMode='contain'
  //     // posterResizeMode="contain"
  //     // poster="https://www.astrogo.com.my/astrogo/images/astrogo-logo.png"
  //     rate={1.0}
  //     volume={1.0}
  //     muted={false}
  //     paused={false}
  //     repeat={false}
  //     onLoad={() => console.log('onLoad')}
  //     onProgress={() => console.log('onProgress')}
  //     onEnd={() => console.log('onEnd')}
  //     onError={(e) => console.log('onError', e)}
  //     onBuffer={() => console.log('onBuffer')}
  //     onTimedMetadata={() => console.log('onTimedMetadata')}
  //   />
  // );
  return (
    <Video
      source={{ uri: sourceUril, type: Platform.OS == 'ios' ? 'm3u8' : 'mpd' }}
      // drm={drm}
      drm={{
        // offlineDrm: 'a3NpZEE1NDYyQjAz',
        type: utils?.isIOS ? DRMType.FAIRPLAY : DRMType.WIDEVINE,
        headers: {
          // Authorization: tokens?.drmToken ? 'Bearer ' + tokens?.drmToken : '', // Did it because if not passing Authorization key it's taking value from source header in android
          // ...(tokens?.drmToken && {
          //   Authorization: 'Bearer ' + tokens?.drmToken,
          // }), //reverting above changes since drmToken is coming true if the content is not drm enabled
          ...(utils?.isIOS && {
            'Content-Type': 'application/x-www-form-urlencoded',
            Accept: 'application/base64',
          }),
        },
        // certificateUrl: list[0]?.,
        licenseServer: list[index]?.mLicenseAcquisitionURL,
        offlineDrm: Platform.OS == 'ios' ? null : list[index]?.keySetId,
      }}
      style={{ width: 300, height: 300 }}
      controls={true}
      resizeMode='contain'
      // posterResizeMode="contain"
      // poster="https://www.astrogo.com.my/astrogo/images/astrogo-logo.png"
      rate={1.0}
      volume={1.0}
      muted={false}
      paused={false}
      repeat={false}
      onLoad={() => console.log('onLoad')}
      onProgress={() => console.log('onProgress')}
      onEnd={() => console.log('onEnd')}
      onError={(e) => console.log('onError', e)}
      onBuffer={() => console.log('onBuffer')}
      onTimedMetadata={() => console.log('onTimedMetadata')}
    />
  );
};

let source = `https://vod.bpk.dev.xp.irdeto.com/bpk-vod/vodc/default/e00cf83e7e0094bc1bba8b28e0ec3517-e68e70ff2605197c052e-1/e00cf83e7e0094bc1bba8b28e0ec3517-e68e70ff2605197c052e-1/index.m3u8`;
//
const drm = {
  certificateUrl:
    'https://irdetoexperience.test.ott.irdeto.com/licenseServer/streaming/v1/irdetoexperience/getcertificate?applicationId=irdetoexperience',
  headers: {
    Accept: 'application/base64',
    'Content-Type': 'application/x-www-form-urlencoded',
  },
  licenseServer:
    'https://irexp-preint.test.ott.irdeto.com/licenseServer/widevine/v1/irexp-preint/license?contentId=e00cf83e7e0094bc1bba8b28e0ec3517-e68e70ff2605197c052e-1',
  type: 'fairplay',
};

let s = {
  audioLanguage: '',
  bitrate: '720',
  certificateURL:
    'https://irdetoexperience.test.ott.irdeto.com/licenseServer/streaming/v1/irdetoexperience/getcertificate?applicationId=irdetoexperience',
  contentType: 'HLS',
  licenseUrl:
    'https://irexp-preint.test.ott.irdeto.com/licenseServer/widevine/v1/irexp-preint/license?contentId=e00cf83e7e0094bc1bba8b28e0ec3517-e68e70ff2605197c052e-1',
  mIsStartOver: true,
  protectionType: 'fairplay',
  subtitleLanguage: '',
  title: 'DownloadOneItem2',
  url: 'https://vod.bpk.dev.xp.irdeto.com/bpk-vod/vodc/default/e00cf83e7e0094bc1bba8b28e0ec3517-e68e70ff2605197c052e-1/e00cf83e7e0094bc1bba8b28e0ec3517-e68e70ff2605197c052e-1/index.m3u8',
};
