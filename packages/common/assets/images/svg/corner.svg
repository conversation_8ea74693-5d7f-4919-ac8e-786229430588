<svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_4629_196933)" filter="url(#filter0_d_4629_196933)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M25.9999 11.6004C22.1808 11.6004 18.5181 13.1175 15.8176 15.8181C13.117 18.5186 11.5999 22.1813 11.5999 26.0004V38.0004C11.5999 38.884 10.8836 39.6004 9.9999 39.6004C9.11625 39.6004 8.3999 38.884 8.3999 38.0004V26.0004C8.3999 21.3326 10.2542 16.856 13.5548 13.5553C16.8555 10.2547 21.3321 8.40039 25.9999 8.40039H37.9999C38.8836 8.40039 39.5999 9.11673 39.5999 10.0004C39.5999 10.884 38.8836 11.6004 37.9999 11.6004H25.9999Z" fill="#EBEBEB"/>
</g>
<defs>
<filter id="filter0_d_4629_196933" x="-4" y="-4" width="56" height="56" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.8 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_4629_196933"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_4629_196933" result="shape"/>
</filter>
<clipPath id="clip0_4629_196933">
<rect width="48" height="48" fill="white"/>
</clipPath>
</defs>
</svg>
