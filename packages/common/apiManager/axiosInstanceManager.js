// 📦 A map of API groups to instances
import { accountManagerApi } from "./apiConfig";
import { amInstance, instance } from "./axios";

const apiRoutingMap = [
  { urls: accountManagerApi, axiosInstance: amInstance },
  // other mapping can go here
];

export const getAxiosInstance = (url = "") => {
  for (let { urls, axiosInstance } of apiRoutingMap) {
    if (urls.some((path) => url.startsWith(path))) {
      // Add a request interceptor to mark the config as an AM request if it's amInstance
      if (axiosInstance === amInstance) {
        axiosInstance.interceptors.request.use(
          (config) => {
            // Mark this as an AM request
            config.isAMInstance = true;
            return config;
          },
          (error) => Promise.reject(error)
        );
      }
      return axiosInstance;
    }
  }

  // 🔁 Default fallback
  return instance;
};
